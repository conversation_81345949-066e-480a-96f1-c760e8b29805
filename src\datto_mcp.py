#!/usr/bin/env python3
"""
Datto Partner MCP Server - Production Ready
Enhanced with intelligent features, full configuration support, and partner customization.
"""

import os
import sys
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from collections import defaultdict
from functools import lru_cache
import time

import httpx
from pydantic import BaseModel, Field
from fastmcp import FastMCP

# Add src directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import get_config, PartnerConfig
from models import *
from dashboard import generate_executive_dashboard
from qbr import QBRGenerator
from utils import setup_logging

# Initialize configuration
config = get_config()

# Setup logging
setup_logging(config.logging)
logger = logging.getLogger(__name__)

# Initialize FastMCP with partner name
mcp = FastMCP(f"datto-partner-portal-{config.partner.initials.lower()}")

# ============================================================================
# Enhanced HTTP Client with Configuration
# ============================================================================

class DattoClient:
    """Enhanced HTTP client for Datto API with configuration support."""

    def __init__(self, config: PartnerConfig):
        self.config = config
        self.client = httpx.AsyncClient(
            timeout=config.performance.api_timeout_seconds,
            base_url=config.datto.base_url,
            auth=(config.datto.public_key, config.datto.secret_key)
        )
        self.rate_limit_remaining = config.datto.rate_limit_budget
        self.logger = logging.getLogger(f"{__name__}.DattoClient")
        self._cache = {}
        self._cache_timestamps = {}
        self._request_times = []  # For rate limiting tracking
    
    def _get_cache_key(self, method: str, endpoint: str, **kwargs) -> str:
        """Generate cache key for request."""
        import hashlib
        key_data = f"{method}:{endpoint}:{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self._cache_timestamps:
            return False

        cache_age = time.time() - self._cache_timestamps[cache_key]
        max_age = self.config.performance.cache_duration_minutes * 60
        return cache_age < max_age

    async def _check_rate_limit(self):
        """Check and enforce rate limiting."""
        current_time = time.time()
        # Remove requests older than 1 hour
        self._request_times = [t for t in self._request_times if current_time - t < 3600]

        # Check if we're approaching rate limit
        if len(self._request_times) >= self.config.datto.rate_limit_budget * 0.9:
            self.logger.warning("Approaching API rate limit, throttling requests")
            await asyncio.sleep(1)  # Simple throttling

        self._request_times.append(current_time)

    async def request(self, method: str, endpoint: str, use_cache: bool = True, **kwargs) -> Dict[str, Any]:
        """Make authenticated request with caching and rate limiting."""
        # Check cache for GET requests
        if method == "GET" and use_cache:
            cache_key = self._get_cache_key(method, endpoint, **kwargs)
            if self._is_cache_valid(cache_key):
                self.logger.debug(f"Cache hit for {endpoint}")
                return self._cache[cache_key]

        # Rate limiting check
        await self._check_rate_limit()

        self.logger.debug(f"Making {method} request to {endpoint}")

        try:
            response = await self.client.request(method, endpoint, **kwargs)

            if 'X-API-Limit-Remaining' in response.headers:
                self.rate_limit_remaining = int(response.headers['X-API-Limit-Remaining'])
                if self.rate_limit_remaining < 100:
                    self.logger.warning(f"API rate limit low: {self.rate_limit_remaining} remaining")

            response.raise_for_status()

            if not response.content:
                return {"items": [], "message": "Empty response"}

            data = response.json()

            if isinstance(data, list):
                data = {"items": data}

            # Cache GET responses
            if method == "GET" and use_cache:
                cache_key = self._get_cache_key(method, endpoint, **kwargs)
                self._cache[cache_key] = data
                self._cache_timestamps[cache_key] = time.time()

            return data

        except httpx.HTTPStatusError as e:
            self.logger.error(f"HTTP error {e.response.status_code}: {e.response.text}")
            raise
        except Exception as e:
            self.logger.error(f"Request failed: {str(e)}")
            raise
    
    async def get(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """GET request helper."""
        return await self.request("GET", endpoint, **kwargs)
    
    async def put(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """PUT request helper."""
        return await self.request("PUT", endpoint, **kwargs)

# Initialize client
client = DattoClient(config)

# ============================================================================
# Core MCP Tools with Configuration Support
# ============================================================================

@mcp.tool
async def list_bcdr_devices(
    page: int = 1,
    per_page: int = 100,
    show_hidden: bool = False,
    show_child_reseller: bool = False
) -> Dict[str, Any]:
    """List all BCDR devices (Siris, Alto, NAS) with optional filters."""
    # Input validation
    if page < 1:
        raise ValueError("Page number must be >= 1")
    if per_page < 1 or per_page > 1000:
        raise ValueError("per_page must be between 1 and 1000")

    logger.info(f"Listing BCDR devices: page={page}, per_page={per_page}")

    params = {
        '_page': page,
        '_perPage': min(per_page, 100),
        'showHiddenDevices': 1 if show_hidden else 0,
        'showChildResellerDevices': 1 if show_child_reseller else 0
    }
    
    result = await client.get("/v1/bcdr/device", params=params)
    devices = result.get('items', [])
    
    # Calculate summary
    summary = {
        "total_devices": len(devices),
        "devices_online": len([d for d in devices if d.get('lastSeenDate')]),
        "devices_with_alerts": len([d for d in devices if d.get('alertCount', 0) > 0]),
        "total_agents": sum(d.get('agentCount', 0) for d in devices),
        "total_shares": sum(d.get('shareCount', 0) for d in devices)
    }
    
    result['summary'] = summary
    result['partner'] = config.partner.name
    return result

@mcp.tool
async def analyze_backup_health() -> Dict[str, Any]:
    """Analyze overall backup health across all devices with partner context."""
    logger.info("Analyzing backup health")
    
    devices = await list_bcdr_devices()
    
    # Calculate health score using configured thresholds
    alert_ratio = devices['summary']['devices_with_alerts'] / max(devices['summary']['total_devices'], 1)
    health_score = max(0, 100 - (alert_ratio * 100))
    
    # Determine health status
    if health_score >= 95:
        status = "Excellent"
    elif health_score >= 85:
        status = "Good"
    elif health_score >= 70:
        status = "Fair"
    else:
        status = "Poor"
    
    recommendations = []
    if devices['summary']['devices_with_alerts'] > 0:
        recommendations.append(f"Address alerts on {devices['summary']['devices_with_alerts']} devices")
        recommendations.append(f"Contact {config.partner.support_email} for immediate assistance")
    
    if not recommendations:
        recommendations.append("All systems operating normally")
        recommendations.append(f"Monitored by {config.partner.name}")
    
    return {
        "overall_health_score": round(health_score, 2),
        "health_status": status,
        "partner_name": config.partner.name,
        "support_contact": config.partner.support_email,
        "bcdr_health": {
            "total_devices": devices['summary']['total_devices'],
            "devices_with_alerts": devices['summary']['devices_with_alerts'],
            "total_protected_assets": devices['summary']['total_agents'] + devices['summary']['total_shares']
        },
        "recommendations": recommendations,
        "timestamp": datetime.now().isoformat()
    }

@mcp.tool
async def correlate_alerts() -> AlertAnalysis:
    """🚨 AI-powered alert correlation across all devices with configurable thresholds."""
    if not config.features.intelligent_alert_correlation:
        return AlertAnalysis(
            total_alerts=0,
            correlations=[],
            isolated_alerts=[],
            noise_reduction_percentage=0,
            actionable_items=["Feature disabled in configuration"],
            timestamp=datetime.now()
        )
    
    logger.info("Starting intelligent alert correlation")
    
    # Get all devices and their alerts
    devices_result = await client.get("/v1/bcdr/device")
    devices = devices_result.get('items', [])
    
    all_alerts = []
    devices_with_alerts = []
    
    for device in devices:
        if device.get('alertCount', 0) > 0:
            serial = device.get('serialNumber')
            device_name = device.get('name', 'Unknown')
            
            try:
                alerts_result = await client.get(f"/v1/bcdr/device/{serial}/alert")
                device_alerts = alerts_result.get('items', [])
                
                for alert in device_alerts:
                    alert['deviceName'] = device_name
                    alert['deviceSerial'] = serial
                    alert['deviceOrg'] = device.get('organizationName', 'Unknown')
                    all_alerts.append(alert)
                
                if device_alerts:
                    devices_with_alerts.append({
                        "deviceName": device_name,
                        "deviceSerial": serial,
                        "alertCount": len(device_alerts),
                        "organizationName": device.get('organizationName', 'Unknown')
                    })
            except Exception as e:
                logger.warning(f"Failed to get alerts for device {serial}: {e}")
                continue
    
    # Use configurable correlation window
    correlation_window = config.alerts.correlation_window_hours
    noise_threshold = config.alerts.noise_reduction_threshold
    critical_threshold = config.alerts.critical_device_threshold
    
    # Correlation analysis with configured parameters
    correlations = []
    alert_groups = defaultdict(list)
    
    for alert in all_alerts:
        alert_type = alert.get('type', 'Unknown')
        date_triggered = alert.get('dateTriggered', '')
        
        try:
            alert_time = datetime.fromisoformat(date_triggered.replace('Z', '+00:00'))
            time_bucket = alert_time.replace(
                hour=(alert_time.hour // correlation_window) * correlation_window,
                minute=0, second=0, microsecond=0
            )
            
            group_key = f"{alert_type}_{time_bucket.isoformat()}"
            alert_groups[group_key].append(alert)
        except (ValueError, AttributeError):
            group_key = f"{alert_type}_unknown_time"
            alert_groups[group_key].append(alert)
    
    # Identify correlations using configured threshold
    correlation_id = 1
    isolated_alerts = []
    
    for group_key, group_alerts in alert_groups.items():
        if len(group_alerts) >= noise_threshold:
            affected_devices = list(set(alert['deviceSerial'] for alert in group_alerts))
            organizations = list(set(alert['deviceOrg'] for alert in group_alerts))
            
            root_cause = _analyze_root_cause(group_alerts, organizations, config)
            suggested_actions = _suggest_actions(group_alerts, root_cause, config)
            
            # Get time range
            times = []
            for alert in group_alerts:
                try:
                    times.append(datetime.fromisoformat(alert.get('dateTriggered', '').replace('Z', '+00:00')))
                except (ValueError, AttributeError):
                    pass
            
            if times:
                first_time = min(times)
                last_time = max(times)
                timeframe = f"{first_time.strftime('%H:%M')} - {last_time.strftime('%H:%M')}"
            else:
                timeframe = "Unknown timeframe"
            
            # Determine severity using configured threshold
            severity = AlertSeverity.CRITICAL if len(affected_devices) >= critical_threshold else AlertSeverity.WARNING
            
            correlations.append(AlertCorrelation(
                correlation_id=str(correlation_id),
                alert_count=len(group_alerts),
                affected_devices=affected_devices,
                common_timeframe=timeframe,
                probable_root_cause=root_cause,
                suggested_actions=suggested_actions,
                severity=severity,
                first_occurrence=first_time if times else datetime.now(),
                last_occurrence=last_time if times else datetime.now()
            ))
            correlation_id += 1
        else:
            isolated_alerts.extend(group_alerts)
    
    # Calculate noise reduction
    original_alert_count = len(all_alerts)
    correlated_alert_count = sum(len(c.affected_devices) for c in correlations)
    noise_reduction = ((original_alert_count - correlated_alert_count) / max(original_alert_count, 1)) * 100 if original_alert_count > 0 else 0
    
    # Generate actionable items
    actionable_items = []
    for correlation in correlations:
        if correlation.severity == AlertSeverity.CRITICAL:
            actionable_items.append(f"URGENT: {correlation.probable_root_cause} affecting {len(correlation.affected_devices)} devices")
    
    if len(correlations) > 0:
        actionable_items.append(f"Contact {config.partner.support_email} for correlation analysis")
        actionable_items.append(f"Investigate {len(correlations)} alert patterns for systemic issues")
    
    logger.info(f"Alert correlation complete: {len(correlations)} correlations, {noise_reduction:.1f}% noise reduction")
    
    return AlertAnalysis(
        total_alerts=original_alert_count,
        correlations=sorted(correlations, key=lambda x: x.alert_count, reverse=True),
        isolated_alerts=isolated_alerts[:10],
        noise_reduction_percentage=noise_reduction,
        actionable_items=actionable_items,
        timestamp=datetime.now()
    )

def _analyze_root_cause(alerts: List[Dict], organizations: List[str], config: PartnerConfig) -> str:
    """Analyze probable root cause with partner context."""
    alert_types = [alert.get('type', '') for alert in alerts]
    alert_messages = [alert.get('messageEN', '') for alert in alerts]
    
    if 'Device Not Seen' in ' '.join(alert_types):
        if len(organizations) == 1:
            return f"Network/infrastructure issue at {organizations[0]} - {config.partner.name} investigating"
        else:
            return f"Widespread connectivity issues - {config.partner.name} responding"
    
    if 'Backup' in ' '.join(alert_types + alert_messages):
        return f"Storage/backup infrastructure problem - {config.partner.name} backup team notified"
    
    if len(set(alert_types)) == 1:
        return f"Systemic {alert_types[0]} issue - {config.partner.name} escalating"
    
    return f"Multiple infrastructure issues - {config.partner.name} investigating"

def _suggest_actions(alerts: List[Dict], root_cause: str, config: PartnerConfig) -> List[str]:
    """Suggest remediation actions with partner contact info."""
    actions = []
    
    if "network" in root_cause.lower():
        actions.append("Check network connectivity and firewall rules")
        actions.append("Verify internet connection at affected locations")
    
    if "backup" in root_cause.lower():
        actions.append("Check storage capacity and backup schedules")
        actions.append("Verify backup service status")
    
    if "infrastructure" in root_cause.lower():
        actions.append("Contact infrastructure team for system status")
        actions.append("Check for planned maintenance windows")
    
    # Add partner-specific actions
    actions.append(f"Contact {config.partner.support_email} for immediate assistance")
    actions.append(f"Emergency support: {config.partner.emergency_phone}")
    
    return actions

# ============================================================================
# Resources with Partner Branding
# ============================================================================

@mcp.resource("datto://executive-dashboard")
async def executive_dashboard() -> str:
    """Token-efficient executive dashboard with partner branding."""
    logger.info("Generating executive dashboard")
    return await generate_executive_dashboard(config, client)

@mcp.resource("datto://partner-info")
async def partner_info() -> Dict[str, Any]:
    """Get partner/MSP information and branding."""
    return {
        "partner": {
            "name": config.partner.name,
            "initials": config.partner.initials,
            "support_email": config.partner.support_email,
            "emergency_phone": config.partner.emergency_phone,
            "website": config.partner.website,
            "logo_url": config.partner.logo_url
        },
        "features_enabled": {
            "intelligent_alerts": config.features.intelligent_alert_correlation,
            "predictive_risk": config.features.predictive_risk_assessment,
            "saas_optimization": config.features.saas_license_optimization,
            "screenshot_verification": config.features.screenshot_verification,
            "interactive_qbr": config.features.interactive_qbr,
            "business_impact": config.features.business_impact_calculations
        },
        "support_info": {
            "documentation": "https://docs.datto.com/partners/mcp",
            "api_docs": "https://api.datto.com/docs",
            "partner_portal": "https://partners.datto.com"
        }
    }

# ============================================================================
# QBR Generation with Partner Configuration
# ============================================================================

@mcp.tool
async def create_qbr(
    client_name: str,
    quarter: Optional[str] = None,
    include_recovery_events: bool = True
) -> str:
    """🎯 Create a comprehensive Quarterly Business Review with partner branding."""
    if not config.features.interactive_qbr:
        return "QBR generation is disabled in configuration"
    
    logger.info(f"Creating QBR for {client_name}")
    
    qbr_generator = QBRGenerator(config, client)
    return await qbr_generator.generate_qbr(
        client_name=client_name,
        quarter=quarter,
        include_recovery_events=include_recovery_events
    )

@mcp.tool
async def calculate_business_impact(
    recovery_type: str,
    downtime_hours: float,
    employees_affected: int,
    ransom_demand: Optional[float] = None
) -> Dict[str, Any]:
    """💰 Calculate business impact using partner-configured cost models."""
    if not config.features.business_impact_calculations:
        return {"error": "Business impact calculations disabled in configuration"}
    
    logger.info(f"Calculating business impact: {recovery_type}, {downtime_hours}h, {employees_affected} employees")
    
    qbr_generator = QBRGenerator(config, client)
    return qbr_generator.calculate_business_impact(
        recovery_type=recovery_type,
        downtime_hours=downtime_hours,
        employees_affected=employees_affected,
        ransom_demand=ransom_demand
    )

# ============================================================================
# Health Check and System Status
# ============================================================================

@mcp.tool
async def system_health_check() -> Dict[str, Any]:
    """Comprehensive system health check with partner context."""
    logger.info("Performing system health check")
    
    health_check = {
        "timestamp": datetime.now().isoformat(),
        "partner": config.partner.name,
        "mcp_version": "2.0.0",
        "api_status": "unknown",
        "features_enabled": vars(config.features),
        "rate_limit_remaining": client.rate_limit_remaining,
        "configuration_valid": True
    }
    
    # Test API connectivity
    try:
        devices = await list_bcdr_devices(per_page=1)
        health_check["api_status"] = "healthy"
        health_check["api_response_time"] = "< 1s"
    except Exception as e:
        health_check["api_status"] = "error"
        health_check["api_error"] = str(e)
    
    # Check log file
    try:
        log_path = config.logging.file
        if os.path.exists(log_path):
            stat = os.stat(log_path)
            health_check["log_file_size_mb"] = round(stat.st_size / 1024 / 1024, 2)
            health_check["log_last_modified"] = datetime.fromtimestamp(stat.st_mtime).isoformat()
    except Exception:
        health_check["log_status"] = "not_accessible"
    
    return health_check

if __name__ == "__main__":
    logger.info(f"Starting Datto Partner MCP Server for {config.partner.name}")
    logger.info(f"Features enabled: {[k for k, v in vars(config.features).items() if v]}")
    mcp.run()