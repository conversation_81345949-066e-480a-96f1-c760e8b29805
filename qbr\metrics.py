"""
QBR Metrics and Business Impact Calculations
Configurable business impact calculations for quarterly business reviews.
"""

from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel
from enum import Enum


class RecoveryType(str, Enum):
    """Types of recovery events."""
    RANSOMWARE = "ransomware"
    HARDWARE_FAILURE = "hardware_failure"
    HUMAN_ERROR = "human_error"
    NATURAL_DISASTER = "natural_disaster"
    CYBER_ATTACK = "cyber_attack"
    SOFTWARE_CORRUPTION = "software_corruption"


class RecoveryEvent(BaseModel):
    """Recovery event details for business impact calculations."""
    event_date: datetime
    recovery_type: RecoveryType
    affected_systems: List[str]
    downtime_hours: float
    employees_affected: int
    ransom_demand: Optional[float] = None
    recovery_successful: bool = True
    data_loss_gb: float = 0
    recovery_time_minutes: int
    business_impact_description: str


class QBRMetrics(BaseModel):
    """QBR metrics and calculations."""
    client_name: str
    quarter: str
    total_devices: int
    total_users: int
    total_data_protected_gb: float
    uptime_percentage: float
    successful_backups: int
    failed_backups: int
    recovery_events: List[RecoveryEvent]
    monthly_service_cost: float
    
    # Calculated values
    downtime_cost_avoided: float = 0
    ransom_cost_avoided: float = 0
    productivity_cost_avoided: float = 0
    compliance_value: float = 0
    total_roi: float = 0
    saas_data_protected_gb: float = 0


class BusinessImpactCalculator:
    """Configurable business impact calculations for QBR generation."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.qbr_config = config.get('qbr', {})
        self.business_impact_config = self.qbr_config.get('business_impact', {})
        self.recovery_times_config = self.qbr_config.get('recovery_times', {})
        self.compliance_config = self.qbr_config.get('compliance', {})
    
    def calculate_business_size(self, employee_count: int) -> str:
        """Determine business size category for cost calculations."""
        if employee_count <= 100:
            return "small_business"
        elif employee_count <= 1000:
            return "mid_market"
        else:
            return "enterprise"
    
    def get_downtime_cost_per_hour(self, employee_count: int) -> float:
        """Calculate hourly downtime cost based on employee count and config."""
        employee_hourly_cost = self.business_impact_config.get('employee_hourly_cost', 75.0)
        productivity_loss = self.business_impact_config.get('productivity_loss_multiplier', 0.85)
        it_staff_cost = self.business_impact_config.get('it_staff_hourly_cost', 125.0)
        
        # Base calculation: employees * hourly cost * productivity loss
        employee_cost = employee_count * employee_hourly_cost * productivity_loss
        
        # Add IT staff response cost
        it_response_hours = self.business_impact_config.get('average_incident_response_hours', 4.0)
        it_cost = it_staff_cost * it_response_hours
        
        return employee_cost + it_cost
    
    def calculate_downtime_cost(self, hours: float, employee_count: int) -> float:
        """Calculate cost of downtime based on configurable rates."""
        hourly_cost = self.get_downtime_cost_per_hour(employee_count)
        return hours * hourly_cost
    
    def calculate_ransomware_impact(self, ransom_demand: float, downtime_hours: float, employee_count: int) -> Dict[str, float]:
        """Calculate total ransomware impact costs using configured multipliers."""
        if ransom_demand == 0:
            ransom_demand = self.business_impact_config.get('default_ransom_demand', 220000.0)
        
        # Total ransomware cost using configured multiplier
        cost_multiplier = self.business_impact_config.get('ransomware_cost_multiplier', 4.62)
        total_ransomware_cost = ransom_demand * cost_multiplier
        
        # Add downtime costs
        downtime_cost = self.calculate_downtime_cost(downtime_hours, employee_count)
        
        return {
            "ransom_demand": ransom_demand,
            "total_ransomware_cost": total_ransomware_cost,
            "downtime_cost": downtime_cost,
            "total_impact": total_ransomware_cost + downtime_cost
        }
    
    def calculate_compliance_value(self, user_count: int, industry: str = "general") -> float:
        """Calculate compliance value based on user count and industry."""
        per_user_value = 150.0  # Base per-user compliance value
        audit_cost_avoided = 25000.0  # Audit costs avoided
        
        # Industry-specific compliance costs
        if industry.lower() in ["healthcare", "medical"]:
            violation_cost = self.compliance_config.get('hipaa_violation_average', 2300000)
            per_user_value *= 2.0  # Higher compliance requirements
        elif industry.lower() in ["finance", "banking", "payment"]:
            violation_cost = self.compliance_config.get('pci_violation_average', 190000)
            per_user_value *= 1.8
        elif industry.lower() in ["european", "eu", "gdpr"]:
            violation_cost = self.compliance_config.get('gdpr_violation_average', 4800000)
            per_user_value *= 2.2
        else:
            violation_cost = self.compliance_config.get('sox_violation_average', 1500000)
        
        # Calculate risk mitigation value (percentage of potential violation cost)
        risk_mitigation = violation_cost * 0.05  # 5% risk mitigation value
        
        return (user_count * per_user_value) + audit_cost_avoided + risk_mitigation
    
    def calculate_storage_roi(self, data_protected_gb: float) -> float:
        """Calculate storage ROI based on protected data volume."""
        storage_config = self.qbr_config.get('storage_costs', {})
        
        cost_per_gb = storage_config.get('cost_per_gb_month', 0.12)
        dedup_ratio = storage_config.get('deduplication_ratio', 3.5)
        alternative_multiplier = storage_config.get('alternative_storage_cost_multiplier', 1.8)
        
        # Monthly savings from deduplication and efficiency
        monthly_datto_cost = data_protected_gb * cost_per_gb
        monthly_alternative_cost = data_protected_gb * cost_per_gb * alternative_multiplier
        
        # Annual ROI calculation
        annual_savings = (monthly_alternative_cost - monthly_datto_cost) * 12
        efficiency_savings = (data_protected_gb / dedup_ratio) * cost_per_gb * 12
        
        return annual_savings + efficiency_savings
    
    def calculate_qbr_metrics(self, raw_data: Dict[str, Any], recovery_events: List[RecoveryEvent]) -> QBRMetrics:
        """Calculate comprehensive QBR metrics from raw data and recovery events."""
        
        # Basic metrics from raw data
        total_devices = raw_data.get('devices', {}).get('total_devices', 0)
        total_users = raw_data.get('users', {}).get('total_users', 0)
        total_data_gb = raw_data.get('total_data_gb', 0)
        saas_data_gb = raw_data.get('saas_data_gb', 0)
        successful_backups = raw_data.get('successful_backups', 0)
        failed_backups = raw_data.get('failed_backups', 0)
        
        # Calculate uptime percentage
        total_backups = successful_backups + failed_backups
        uptime_percentage = (successful_backups / max(total_backups, 1)) * 100 if total_backups > 0 else 100.0
        
        # Calculate business impact values
        total_downtime_avoided = 0
        total_ransom_avoided = 0
        
        for event in recovery_events:
            if event.recovery_type == RecoveryType.RANSOMWARE:
                impact = self.calculate_ransomware_impact(
                    event.ransom_demand or 0,
                    event.downtime_hours,
                    event.employees_affected
                )
                total_ransom_avoided += impact['total_impact']
            else:
                downtime_cost = self.calculate_downtime_cost(
                    event.downtime_hours,
                    event.employees_affected
                )
                total_downtime_avoided += downtime_cost
        
        # Calculate compliance value
        compliance_value = self.calculate_compliance_value(total_users)
        
        # Calculate storage ROI
        storage_roi = self.calculate_storage_roi(total_data_gb)
        
        # Total ROI calculation
        total_roi = total_downtime_avoided + total_ransom_avoided + compliance_value + storage_roi
        
        # Estimate monthly service cost (if not provided)
        monthly_service_cost = raw_data.get('monthly_service_cost', 0)
        if monthly_service_cost == 0:
            # Estimate based on devices and users
            device_cost = total_devices * 150  # $150/device/month average
            user_cost = total_users * 4  # $4/user/month for SaaS
            monthly_service_cost = device_cost + user_cost
        
        # Generate current quarter
        current_quarter = f"Q{((datetime.now().month - 1) // 3) + 1}"
        quarter = f"{current_quarter} {datetime.now().year}"
        
        return QBRMetrics(
            client_name=raw_data.get('client_name', 'Unknown Client'),
            quarter=quarter,
            total_devices=total_devices,
            total_users=total_users,
            total_data_protected_gb=total_data_gb,
            uptime_percentage=round(uptime_percentage, 2),
            successful_backups=successful_backups,
            failed_backups=failed_backups,
            recovery_events=recovery_events,
            monthly_service_cost=monthly_service_cost,
            downtime_cost_avoided=total_downtime_avoided,
            ransom_cost_avoided=total_ransom_avoided,
            productivity_cost_avoided=0,  # Additional calculation if needed
            compliance_value=compliance_value,
            total_roi=total_roi,
            saas_data_protected_gb=saas_data_gb
        )