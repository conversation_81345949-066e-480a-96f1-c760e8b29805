# Client Setup Guide

This guide covers how to connect to and use the Datto Partner Portal MCP Server from various client applications.

## Overview

The Datto Partner Portal MCP Server implements the Model Context Protocol (MCP), allowing AI applications and clients to interact with <PERSON><PERSON>'s backup infrastructure through standardized tools and resources.

## Supported Clients

### Claude Desktop

The most common way to use MCP servers is through Claude Desktop, which has built-in MCP support.

#### Setup Instructions

1. **Install Claude Desktop** (if not already installed)
   - Download from [Claude.ai](https://claude.ai/download)
   - Available for Windows, Mac, and Linux

2. **Locate Claude Configuration**
   - **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
   - **Mac**: `~/Library/Application Support/Claude/claude_desktop_config.json`
   - **Linux**: `~/.config/Claude/claude_desktop_config.json`

3. **Add MCP Server Configuration**

   Create or edit the `claude_desktop_config.json` file:

   ```json
   {
     "mcpServers": {
       "datto-partner-portal": {
         "command": "python",
         "args": ["/path/to/partner-ready-dpp-mcp/src/dpp_mcp_server.py"],
         "env": {
           "DATTO_PUBLIC_KEY": "your_public_key_here",
           "DATTO_SECRET_KEY": "your_secret_key_here"
         }
       }
     }
   }
   ```

   **Important**: Replace `/path/to/partner-ready-dpp-mcp` with the actual path to your installation.

4. **Alternative: Configuration File Method**

   If you prefer to use the YAML configuration file instead of environment variables:

   ```json
   {
     "mcpServers": {
       "datto-partner-portal": {
         "command": "python",
         "args": ["/path/to/partner-ready-dpp-mcp/src/dpp_mcp_server.py"],
         "cwd": "/path/to/partner-ready-dpp-mcp"
       }
     }
   }
   ```

   Make sure your `config/dpp-config.yaml` file has the correct API credentials.

5. **Restart Claude Desktop**

   Completely close and reopen Claude Desktop for the configuration to take effect.

6. **Verify Connection**

   In Claude Desktop, you should see the Datto MCP tools available. Try asking:
   
   > "Can you analyze the backup health for our Datto environment?"

### VS Code with MCP Extension

If you're using VS Code with an MCP extension:

1. **Install MCP Extension** from the VS Code marketplace

2. **Configure in VS Code Settings**

   Add to your VS Code `settings.json`:

   ```json
   {
     "mcp.servers": {
       "datto-partner-portal": {
         "command": "python",
         "args": ["/path/to/partner-ready-dpp-mcp/src/dpp_mcp_server.py"],
         "env": {
           "DATTO_PUBLIC_KEY": "your_public_key_here",
           "DATTO_SECRET_KEY": "your_secret_key_here"
         }
       }
     }
   }
   ```

### Other MCP Clients

For custom applications or other MCP clients:

1. **MCP Protocol**: The server implements MCP version 2024-11-05
2. **Transport**: Standard stdio transport
3. **Capabilities**: tools, resources, and logging

#### Connection Example (Python)

```python
import asyncio
import subprocess
from mcp import ClientSession, StdioServerParameters

async def connect_to_datto_mcp():
    server_params = StdioServerParameters(
        command="python",
        args=["/path/to/partner-ready-dpp-mcp/src/dpp_mcp_server.py"],
        env={
            "DATTO_PUBLIC_KEY": "your_public_key_here",
            "DATTO_SECRET_KEY": "your_secret_key_here"
        }
    )
    
    async with ClientSession(server_params) as session:
        # Initialize the connection
        await session.initialize()
        
        # List available tools
        tools = await session.list_tools()
        print(f"Available tools: {[tool.name for tool in tools]}")
        
        # Call a tool
        result = await session.call_tool("analyze_backup_health", {})
        print(f"Health analysis: {result}")

# Run the example
asyncio.run(connect_to_datto_mcp())
```

## Environment Configuration

### Option 1: Environment Variables

Set these environment variables before starting your MCP client:

```bash
# Mac/Linux
export DATTO_PUBLIC_KEY="your_public_key_here"
export DATTO_SECRET_KEY="your_secret_key_here"

# Windows
set DATTO_PUBLIC_KEY=your_public_key_here
set DATTO_SECRET_KEY=your_secret_key_here
```

### Option 2: Configuration File

Ensure your `config/dpp-config.yaml` file contains:

```yaml
api:
  public_key: "your_public_key_here"
  secret_key: "your_secret_key_here"
```

## Available Tools and Usage

Once connected, you can use these MCP tools:

### Backup Health Analysis
```
analyze_backup_health()
```
Returns comprehensive health analysis across all Datto backup products.

### Business Impact Calculations
```
calculate_business_impact(
  recovery_type="ransomware",
  downtime_hours=24.0,
  employees_affected=100,
  ransom_demand=100000
)
```

### QBR Report Generation
```
create_qbr(
  client_name="Example Company",
  msp_name="Your MSP",
  quarter="Q2 2025"
)
```

### Device Management
```
list_bcdr_devices(page=1, per_page=50)
```

### Advanced Analytics
```
predict_backup_risks()
correlate_alerts()
optimize_saas_licensing()
```

### Resources
```
datto://screenshots
datto://screenshots/bcdr-agent
```

## Example Client Usage

### Basic Health Check

In Claude Desktop or your MCP client:

> "Please analyze our backup health and show me any devices that need attention."

This will call the `analyze_backup_health()` tool and provide a comprehensive overview.

### Generate QBR Report

> "Create a quarterly business review for 'Acme Corporation' covering Q2 2025. Include any recent recovery events if available."

This will generate a complete QBR with real API data and business impact calculations.

### Risk Assessment

> "What backup failures are we likely to see in the next 24-48 hours based on current patterns?"

This uses the ML-based risk prediction to identify potential issues.

## Security Considerations

### API Credential Security

1. **Never commit credentials** to version control
2. **Use environment variables** when possible
3. **Restrict file permissions** on configuration files:
   ```bash
   chmod 600 config/dpp-config.yaml
   ```
4. **Use dedicated API accounts** with minimal required permissions

### Network Security

1. **Firewall rules**: Ensure outbound HTTPS (port 443) to `api.datto.com`
2. **Corporate networks**: May require proxy configuration
3. **VPN considerations**: Ensure stable connectivity if using VPN

### Client Security

1. **Validate server responses** in your client application
2. **Handle errors gracefully** to prevent information disclosure
3. **Log access attempts** for audit purposes
4. **Use secure communication** between client and MCP server

## Troubleshooting Client Connections

### Common Issues

#### "MCP server not found" or "Command failed"

**Solutions:**
1. Verify the path to `dpp_mcp_server.py` is correct and absolute
2. Ensure Python is in your system PATH
3. Check that the virtual environment is activated if using one
4. Test the server manually: `python src/dpp_mcp_server.py`

#### "Authentication failed" or "Invalid credentials"

**Solutions:**
1. Verify API credentials are correct
2. Check environment variables are set properly
3. Ensure configuration file has correct YAML syntax
4. Test credentials with: `curl -u "PUBLIC:SECRET" https://api.datto.com/v1/bcdr/device?_perPage=1`

#### "Connection timeout" or "Server unresponsive"

**Solutions:**
1. Check network connectivity to `api.datto.com`
2. Verify firewall allows outbound HTTPS
3. Increase timeout in configuration
4. Review server logs for errors

#### "No tools available" or "Empty response"

**Solutions:**
1. Ensure server started successfully (check logs)
2. Verify MCP client supports the protocol version
3. Check for initialization errors in server logs
4. Try restarting both client and server

### Debug Mode

Enable debug logging to troubleshoot connection issues:

1. **In your configuration file**:
   ```yaml
   logging:
     level: "DEBUG"
   ```

2. **Check server logs**:
   ```bash
   tail -f logs/dpp-mcp.log
   ```

3. **Enable client-side debugging** (varies by client)

### Testing Connection

#### Manual Server Test

```bash
# Start server directly
cd /path/to/partner-ready-dpp-mcp
source venv/bin/activate  # if using virtual environment
python src/dpp_mcp_server.py

# Should see:
# INFO - Starting Datto Partner Portal MCP Server
# INFO - Configuration loaded from: /path/to/config
# INFO - API client initialized successfully
```

#### Test API Connectivity

```bash
# Test basic connectivity
curl -I https://api.datto.com

# Test authentication
curl -u "YOUR_PUBLIC:YOUR_SECRET" "https://api.datto.com/v1/bcdr/device?_perPage=1"
```

## Client Configuration Examples

### Multiple Environment Setup

If you manage multiple Datto environments:

```json
{
  "mcpServers": {
    "datto-production": {
      "command": "python",
      "args": ["/path/to/partner-ready-dpp-mcp/src/dpp_mcp_server.py"],
      "env": {
        "DATTO_PUBLIC_KEY": "prod_public_key",
        "DATTO_SECRET_KEY": "prod_secret_key",
        "DPP_CONFIG_PATH": "/path/to/prod-config.yaml"
      }
    },
    "datto-staging": {
      "command": "python",
      "args": ["/path/to/partner-ready-dpp-mcp/src/dpp_mcp_server.py"],
      "env": {
        "DATTO_PUBLIC_KEY": "staging_public_key",
        "DATTO_SECRET_KEY": "staging_secret_key",
        "DPP_CONFIG_PATH": "/path/to/staging-config.yaml"
      }
    }
  }
}
```

### Performance-Optimized Configuration

For large environments or high-frequency usage:

```json
{
  "mcpServers": {
    "datto-partner-portal": {
      "command": "python",
      "args": ["/path/to/partner-ready-dpp-mcp/src/dpp_mcp_server.py"],
      "env": {
        "DATTO_PUBLIC_KEY": "your_public_key_here",
        "DATTO_SECRET_KEY": "your_secret_key_here",
        "DPP_CONFIG_PATH": "/path/to/high-performance-config.yaml"
      },
      "timeout": 300
    }
  }
}
```

Where `high-performance-config.yaml` contains optimized settings:

```yaml
performance:
  max_devices_per_query: 25
  max_assets_per_device: 5
  screenshot_limit: 4
  backup_history_days: 14

api:
  timeout_seconds: 90
  rate_limit_budget: 0.8
```

## Integration Examples

### Automated Reporting Workflow

```python
# Example: Automated weekly QBR generation
import asyncio
from mcp import ClientSession, StdioServerParameters

async def generate_weekly_reports():
    """Generate QBR reports for all clients automatically."""
    
    server_params = StdioServerParameters(
        command="python",
        args=["/path/to/partner-ready-dpp-mcp/src/dpp_mcp_server.py"],
        env={"DATTO_PUBLIC_KEY": "...", "DATTO_SECRET_KEY": "..."}
    )
    
    clients = ["Acme Corp", "Example Inc", "Test Company"]
    
    async with ClientSession(server_params) as session:
        await session.initialize()
        
        for client in clients:
            try:
                result = await session.call_tool("create_qbr", {
                    "client_name": client,
                    "msp_name": "Your MSP Name",
                    "quarter": "Q2 2025"
                })
                print(f"Generated QBR for {client}: {result}")
            except Exception as e:
                print(f"Failed to generate QBR for {client}: {e}")

# Schedule this to run weekly
asyncio.run(generate_weekly_reports())
```

### Monitoring Dashboard Integration

```python
# Example: Real-time backup health monitoring
async def monitor_backup_health():
    """Monitor backup health and alert on issues."""
    
    while True:
        try:
            async with ClientSession(server_params) as session:
                await session.initialize()
                
                health = await session.call_tool("analyze_backup_health", {})
                
                if health["overall_health_score"] < 70:
                    # Send alert to monitoring system
                    send_alert(f"Backup health score low: {health['overall_health_score']}")
                
                # Check for high-risk assets
                risks = await session.call_tool("predict_backup_risks", {})
                
                if risks["high_risk_assets"]:
                    send_alert(f"High-risk assets detected: {len(risks['high_risk_assets'])}")
                
        except Exception as e:
            print(f"Monitoring error: {e}")
        
        await asyncio.sleep(3600)  # Check every hour

asyncio.run(monitor_backup_health())
```

## Getting Support

For client setup issues:

1. **Check server logs**: `logs/dpp-mcp.log`
2. **Verify configuration**: Use the test scripts in the troubleshooting guide
3. **Test connectivity**: Ensure API access is working
4. **Review documentation**: See [Troubleshooting Guide](troubleshooting.md)
5. **Contact support**: Provide configuration (sanitized) and error messages

The MCP server is designed to be robust and provide helpful error messages to assist with troubleshooting client connection issues.