# Troubleshooting Guide

This guide covers common issues and their solutions for the Datto Partner Portal MCP Server.

## Common Issues

### API Connection Problems

#### Error: "Authentication failed"
**Symptoms:** 401 Unauthorized errors, "Invalid credentials" messages

**Solutions:**
1. Verify API credentials in `config/dpp-config.yaml`
2. Check that public_key and secret_key are correct
3. Ensure API user has required permissions in Datto Portal
4. Test credentials with curl:
   ```bash
   curl -u "PUBLIC_KEY:SECRET_KEY" https://api.datto.com/v1/bcdr/device?_perPage=1
   ```

#### Error: "Connection timeout"
**Symptoms:** Requests hanging, timeout errors

**Solutions:**
1. Check network connectivity to api.datto.com
2. Verify firewall allows HTTPS (port 443) outbound
3. Increase timeout in configuration:
   ```yaml
   api:
     timeout_seconds: 120
   ```
4. For corporate networks, configure proxy settings

#### Error: "Rate limit exceeded"
**Symptoms:** 429 Too Many Requests errors

**Solutions:**
1. Reduce query limits in configuration:
   ```yaml
   performance:
     max_devices_per_query: 25
     max_assets_per_device: 5
   ```
2. Check rate_limit_budget in API configuration
3. Space out API calls by reducing frequency of operations

### Configuration Issues

#### Error: "Configuration file not found"
**Symptoms:** Server won't start, missing config file error

**Solutions:**
1. Copy example configuration:
   ```bash
   cp config/config-example.yaml config/dpp-config.yaml
   ```
2. Verify file path and permissions
3. Check that YAML syntax is valid:
   ```bash
   python -c "import yaml; yaml.safe_load(open('config/dpp-config.yaml'))"
   ```

#### Error: "Invalid YAML syntax"
**Symptoms:** YAML parsing errors on startup

**Solutions:**
1. Use a YAML validator (online or `yamllint`)
2. Check indentation (use spaces, not tabs)
3. Verify quotes around strings with special characters
4. Compare with config-example.yaml structure

### QBR Generation Issues

#### Error: "No backup data found"
**Symptoms:** QBR shows 0 backups or empty data

**Solutions:**
1. Verify API user has access to backup data
2. Check device and asset permissions in Datto Portal
3. Increase backup_history_days in configuration
4. Test backup API endpoints directly

#### Error: "Screenshot URLs not loading"
**Symptoms:** Broken images in QBR reports

**Solutions:**
1. Verify screenshot URLs are accessible from your network
2. Check if screenshots exist for the time period
3. Screenshots may be behind authentication - ensure proper access
4. Test with: `curl -I "SCREENSHOT_URL"`

#### Error: "Business impact calculations seem wrong"
**Symptoms:** Unrealistic cost calculations in QBR

**Solutions:**
1. Review business_impact settings in configuration
2. Adjust employee_hourly_cost for your market
3. Verify ransomware_cost_multiplier is appropriate
4. Check that employee counts are realistic

### Performance Issues

#### Error: "Server is slow or unresponsive"
**Symptoms:** Long response times, timeouts

**Solutions:**
1. Reduce API query limits:
   ```yaml
   performance:
     max_devices_per_query: 25
     max_assets_per_device: 5
     backup_history_limit: 50
   ```
2. Check system resources (CPU, memory, disk)
3. Review logs for errors or bottlenecks
4. Consider caching for frequently accessed data

#### Error: "Memory usage increasing over time"
**Symptoms:** Memory leaks, system slowdown

**Solutions:**
1. Restart the MCP server regularly
2. Check for unclosed connections in logs
3. Reduce query sizes to lower memory usage
4. Monitor with system tools (htop, Task Manager)

### Installation Issues

#### Error: "Python module not found"
**Symptoms:** ImportError when starting server

**Solutions:**
1. Activate virtual environment:
   ```bash
   source venv/bin/activate  # Mac/Linux
   venv\Scripts\activate     # Windows
   ```
2. Reinstall requirements:
   ```bash
   pip install -r requirements.txt
   ```
3. Verify Python version is 3.9+

#### Error: "Permission denied"
**Symptoms:** Cannot write to logs or artifacts directories

**Solutions:**
1. Check directory permissions:
   ```bash
   ls -la logs/ artifacts/
   ```
2. Create directories if missing:
   ```bash
   mkdir -p logs artifacts
   ```
3. On Windows, run as Administrator if needed
4. Verify user has write access to installation directory

## Debugging Techniques

### Enable Debug Logging

```yaml
logging:
  level: "DEBUG"
```

This provides detailed information about API calls, processing steps, and errors.

### Test Individual Components

#### Test API Connection
```bash
python -c "
import asyncio
from src.utils.api_client import DattoClient, DattoConfig

async def test():
    config = DattoConfig(public_key='YOUR_KEY', secret_key='YOUR_SECRET')
    client = DattoClient(config)
    result = await client.get('/v1/bcdr/device?_perPage=1')
    print(f'Success: {len(result.get(\"items\", []))} devices found')
    await client.close()

asyncio.run(test())
"
```

#### Test Configuration Loading
```bash
python -c "
import sys, os
sys.path.append('src')
from dpp_mcp_server import load_config
config = load_config()
print('Configuration loaded successfully')
print(f'API base URL: {config[\"api\"][\"base_url\"]}')
"
```

#### Test Business Calculations
```bash
python -c "
import sys, os
sys.path.append('src')
from qbr.metrics import BusinessImpactCalculator

config = {'qbr': {'business_impact': {'employee_hourly_cost': 75}}}
calc = BusinessImpactCalculator(config)
cost = calc.calculate_downtime_cost(2.0, 100)
print(f'Downtime cost for 2 hours, 100 employees: ${cost:,.2f}')
"
```

### Check API Endpoints

Test specific Datto API endpoints:

```bash
# Test BCDR devices
curl -u "PUBLIC:SECRET" "https://api.datto.com/v1/bcdr/device?_perPage=5"

# Test SaaS domains
curl -u "PUBLIC:SECRET" "https://api.datto.com/v1/saas/domains?_perPage=5"

# Test DTC assets
curl -u "PUBLIC:SECRET" "https://api.datto.com/v1/dtc/assets?_perPage=5"
```

### Monitor Resource Usage

#### Linux/Mac
```bash
# Monitor in real-time
top -p $(pgrep -f dpp_mcp_server)

# Check memory usage
ps aux | grep dpp_mcp_server

# Monitor network connections
netstat -tulpn | grep python
```

#### Windows
```batch
# Task Manager or
tasklist /fi "imagename eq python.exe"

# Resource Monitor for detailed analysis
```

## Log Analysis

### Important Log Messages

**Normal Operation:**
```
INFO - Starting Datto Partner Portal MCP Server
INFO - Configuration loaded from: /path/to/config
INFO - API client initialized successfully
```

**Warning Signs:**
```
WARNING - Rate limit approaching: 95% used
WARNING - API request timeout, retrying...
WARNING - Large response size: 50MB
```

**Error Indicators:**
```
ERROR - Failed to authenticate with Datto API
ERROR - Configuration validation failed
ERROR - Unable to generate QBR: missing data
```

### Log File Locations

- **Default**: `logs/dpp-mcp.log`
- **Rotated logs**: `logs/dpp-mcp.log.1`, `logs/dpp-mcp.log.2`, etc.
- **System logs**: Check system logs for startup/shutdown events

## Network Diagnostics

### Test Connectivity

```bash
# Basic connectivity
ping api.datto.com

# HTTPS connectivity
curl -I https://api.datto.com

# DNS resolution
nslookup api.datto.com

# Trace route
traceroute api.datto.com  # Linux/Mac
tracert api.datto.com     # Windows
```

### Corporate Network Issues

1. **Proxy Configuration**: Add proxy settings to HTTP client
2. **Certificate Issues**: Corporate firewalls may interfere with SSL
3. **DNS Issues**: Internal DNS may not resolve external addresses
4. **Port Blocking**: Port 443 may be restricted

Contact your network administrator if corporate network issues persist.

## Performance Tuning

### For Large Environments (100+ devices)

```yaml
performance:
  max_devices_per_query: 20
  max_assets_per_device: 3
  max_dtc_assets: 25
  backup_history_limit: 30

api:
  timeout_seconds: 90
```

### For High-Frequency Usage

```yaml
performance:
  screenshot_limit: 4
  backup_history_days: 14

logging:
  level: "WARNING"  # Reduce log verbosity
```

### For Resource-Constrained Systems

```yaml
performance:
  max_devices_per_query: 10
  max_assets_per_device: 2
  max_saas_domains: 5

artifacts:
  max_report_size: 25
```

## Getting Additional Help

### Information to Gather

When requesting support, please provide:

1. **Error messages** (exact text from logs)
2. **Configuration** (sanitized - remove API keys)
3. **Environment details** (OS, Python version, network setup)
4. **Steps to reproduce** the issue
5. **Log files** (last 100 lines usually sufficient)

### Log Collection Script

```bash
#!/bin/bash
# collect-logs.sh - Gather diagnostic information

echo "=== System Information ===" > diagnostic.txt
uname -a >> diagnostic.txt
python --version >> diagnostic.txt
pip list | grep -E "(fastmcp|httpx|pydantic|yaml)" >> diagnostic.txt

echo -e "\n=== Configuration (sanitized) ===" >> diagnostic.txt
cat config/dpp-config.yaml | sed 's/public_key:.*/public_key: [REDACTED]/' | sed 's/secret_key:.*/secret_key: [REDACTED]/' >> diagnostic.txt

echo -e "\n=== Recent Logs ===" >> diagnostic.txt
tail -100 logs/dpp-mcp.log >> diagnostic.txt

echo -e "\n=== Network Test ===" >> diagnostic.txt
curl -I https://api.datto.com >> diagnostic.txt 2>&1

echo "Diagnostic information saved to diagnostic.txt"
```

### Support Resources

1. **Documentation**: Review all documentation in `docs/` directory
2. **Configuration examples**: Check `config/config-example.yaml`
3. **Test scripts**: Run tests in `tests/` directory
4. **Datto support**: Contact Datto for API-specific issues
5. **Community**: Check for updates and community solutions

### Escalation Path

1. **Check documentation** and common solutions first
2. **Test with minimal configuration** to isolate issues
3. **Gather diagnostic information** as described above
4. **Contact your Datto partner representative** for API access issues
5. **Report bugs** with detailed reproduction steps