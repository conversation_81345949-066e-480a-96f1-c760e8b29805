# API Reference

Complete reference for all tools and resources available in the Datto Partner MCP Server.

## 🔧 Tools

### Basic Monitoring

#### `list_bcdr_devices`
List all BCDR devices with optional filtering.

**Parameters:**
- `page` (int, optional): Page number (default: 1)
- `per_page` (int, optional): Items per page, max 100 (default: 100)
- `show_hidden` (bool, optional): Include hidden devices (default: false)
- `show_child_reseller` (bool, optional): Include child reseller devices (default: false)

**Returns:**
```json
{
  "items": [
    {
      "serialNumber": "ABC123456",
      "name": "Client-Siris-01",
      "model": "Siris 4",
      "organizationName": "Acme Corp",
      "lastSeenDate": "2024-01-15T10:30:00Z",
      "alertCount": 2,
      "agentCount": 5,
      "shareCount": 3,
      "status": "online"
    }
  ],
  "summary": {
    "total_devices": 15,
    "devices_online": 14,
    "devices_with_alerts": 3,
    "total_agents": 75,
    "total_shares": 45
  },
  "partner": "Your MSP Name"
}
```

#### `analyze_backup_health`
Analyze overall backup health across all devices.

**Parameters:** None

**Returns:**
```json
{
  "overall_health_score": 94.2,
  "health_status": "Excellent",
  "partner_name": "Your MSP Name",
  "support_contact": "<EMAIL>",
  "bcdr_health": {
    "total_devices": 15,
    "devices_with_alerts": 1,
    "total_protected_assets": 120
  },
  "recommendations": [
    "Address alerts on 1 devices",
    "All systems operating normally"
  ],
  "timestamp": "2024-01-15T15:30:00Z"
}
```

#### `system_health_check`
Comprehensive system health check with partner context.

**Parameters:** None

**Returns:**
```json
{
  "timestamp": "2024-01-15T15:30:00Z",
  "partner": "Your MSP Name",
  "mcp_version": "2.0.0",
  "api_status": "healthy",
  "api_response_time": "< 1s",
  "features_enabled": {
    "intelligent_alert_correlation": true,
    "predictive_risk_assessment": true,
    "saas_license_optimization": true,
    "screenshot_verification": true,
    "interactive_qbr": true,
    "business_impact_calculations": true
  },
  "rate_limit_remaining": 9847,
  "configuration_valid": true,
  "log_file_size_mb": 2.3,
  "log_last_modified": "2024-01-15T15:25:00Z"
}
```

### Intelligent Analytics

#### `correlate_alerts`
AI-powered alert correlation across all devices.

**Parameters:** None

**Returns:**
```json
{
  "total_alerts": 25,
  "correlations": [
    {
      "correlation_id": "corr_001",
      "alert_count": 8,
      "affected_devices": ["DEV001", "DEV002", "DEV003"],
      "common_timeframe": "14:30 - 15:45",
      "probable_root_cause": "Network/infrastructure issue at Chicago office - Your MSP investigating",
      "suggested_actions": [
        "Check network connectivity and firewall rules",
        "Verify internet connection at affected locations",
        "Contact <EMAIL> for immediate assistance"
      ],
      "severity": "critical",
      "first_occurrence": "2024-01-15T14:30:00Z",
      "last_occurrence": "2024-01-15T15:45:00Z"
    }
  ],
  "isolated_alerts": [],
  "noise_reduction_percentage": 68.0,
  "actionable_items": [
    "URGENT: Network/infrastructure issue affecting 3 devices",
    "Contact <EMAIL> for correlation analysis"
  ],
  "timestamp": "2024-01-15T15:30:00Z"
}
```

#### `predict_backup_risks`
ML-based prediction of likely backup failures.

**Parameters:** None  

**Returns:**
```json
{
  "high_risk_assets": [
    {
      "asset_id": "DEV001_agent_123",
      "asset_name": "CLIENT-DC-01/Exchange",
      "risk_score": 85.0,
      "risk_factors": [
        "No backup for 36 hours",
        "Screenshot verification failing"
      ],
      "predicted_failure_window": "Next 4-8 hours",
      "recommended_actions": [
        "Immediate investigation required",
        "Check backup infrastructure"
      ],
      "historical_pattern": "Verification issues"
    }
  ],
  "medium_risk_assets": [],
  "low_risk_assets": [],
  "overall_risk_score": 25.5,
  "predictions": [
    "1 assets likely to fail within 8 hours",
    "Primary risk pattern: Verification issues (1 assets)"
  ],
  "preventive_actions": [
    "Immediately investigate 1 high-risk assets",
    "Fix screenshot verification on 1 assets"
  ],
  "timestamp": "2024-01-15T15:30:00Z"
}
```

#### `optimize_saas_licensing`
Analyze and optimize SaaS Protection licensing.

**Parameters:** None

**Returns:**
```json
{
  "total_seats": 156,
  "active_seats": 142,
  "inactive_seats": 14,
  "wasted_licenses": [
    {
      "seat_id": "user_789",
      "seat_name": "acme.com/<EMAIL>",
      "seat_type": "User",
      "days_inactive": 120,
      "potential_monthly_savings": 4.0,
      "recommendation": "Review inactive user, consider pausing"
    }
  ],
  "total_monthly_waste": 168.0,
  "optimization_opportunities": [
    "Review 8 long-inactive seats for optimization",
    "Focus on User seats - highest waste potential ($112/month)"
  ],
  "auto_actions": [
    "Auto-pause 3 high-value inactive seats",
    "Auto-remove 8 long-inactive seats"
  ],
  "timestamp": "2024-01-15T15:30:00Z"
}
```

### Executive Reporting

#### `create_qbr`
Create comprehensive Quarterly Business Review.

**Parameters:**
- `client_name` (str): Client/company name
- `quarter` (str, optional): Quarter (e.g., "Q1 2025", or "auto" for current)
- `include_recovery_events` (bool, optional): Include recovery analysis (default: true)

**Returns:**
```html
<!DOCTYPE html>
<html>
<!-- Complete QBR HTML with partner branding, metrics, and recommendations -->
</html>
```

#### `calculate_business_impact`
Calculate business impact using partner-configured cost models.

**Parameters:**
- `recovery_type` (str): Type of recovery ("ransomware", "hardware_failure", "human_error", etc.)
- `downtime_hours` (float): Hours of downtime
- `employees_affected` (int): Number of employees impacted
- `ransom_demand` (float, optional): Ransom amount for ransomware scenarios

**Returns:**
```json
{
  "recovery_type": "Ransomware Attack",
  "ransom_demand": "$280,000",
  "total_ransomware_cost": "$1,293,600",
  "downtime_cost": "$315,000",
  "total_business_impact": "$1,608,600",
  "partner_saved_client": "$1,608,600",
  "calculation_notes": [
    "Ransom demand: $280,000",
    "Total ransomware costs (4.62x multiplier): $1,293,600",
    "Downtime costs (6 hours): $315,000",
    "Total business impact avoided by Your MSP backup recovery"
  ]
}
```

## 📊 Resources

### `datto://executive-dashboard`
Token-efficient executive dashboard with partner branding.

**Returns:** HTML dashboard with:
- Overall health metrics
- Alert correlation summary  
- Risk assessment overview
- SaaS optimization insights
- Screenshot verification gallery
- Priority action items
- Partner branding and contact info

### `datto://partner-info`
Partner/MSP information and feature status.

**Returns:**
```json
{
  "partner": {
    "name": "Your MSP Name",
    "initials": "MSP",
    "support_email": "<EMAIL>",
    "emergency_phone": "1-800-YOUR-MSP",
    "website": "https://yourmsp.com",
    "logo_url": "https://yourmsp.com/logo.png"
  },
  "features_enabled": {
    "intelligent_alerts": true,
    "predictive_risk": true,
    "saas_optimization": true,
    "screenshot_verification": true,
    "interactive_qbr": true,
    "business_impact": true
  },
  "support_info": {
    "documentation": "https://docs.datto.com/partners/mcp",
    "api_docs": "https://api.datto.com/docs",
    "partner_portal": "https://partners.datto.com"
  }
}
```

### `datto://screenshots`
Recent screenshots from assets supporting verification.

**Returns:**
```json
[
  {
    "name": "CLIENT-DC-01",
    "url": "https://screenshots.datto.com/abc123.png",
    "success": true,
    "device": "Client Siris 4",
    "timestamp": "01/15 14:30",
    "asset_type": "BCDR Agent"
  }
]
```

### `datto://screenshots/{asset_type}`
Screenshots filtered by asset type.

**Parameters:**
- `asset_type`: "bcdr-agent" or "dtc-server"

**Returns:** Filtered screenshot array

### `datto://qbr-template`
Base QBR template for customization.

**Returns:** Raw HTML template with placeholder variables

## 🔧 Configuration Impact

Many tool behaviors are influenced by your partner configuration:

### Alert Correlation
- `alerts.correlation_window_hours`: Time window for grouping alerts
- `alerts.noise_reduction_threshold`: Minimum alerts to create correlation
- `alerts.critical_device_threshold`: Devices affected for critical severity

### Risk Assessment  
- `risk.high_risk_threshold`: Score threshold for high-risk classification
- `risk.backup_age_warning_hours`: Hours before backup age warning
- `risk.screenshot_failure_risk_points`: Risk score impact of screenshot failures

### Business Impact Calculations
- `qbr.cost_calculations.small_business_downtime_cost`: $/hour for 1-100 employees
- `qbr.cost_calculations.mid_market_downtime_cost`: $/hour for 100-1000 employees
- `qbr.cost_calculations.enterprise_downtime_cost`: $/hour for 1000+ employees

### SaaS Optimization
- `saas_pricing.office365.user`: Cost per Office 365 user seat
- `saas_pricing.office365.shared_mailbox`: Cost per shared mailbox
- `saas_pricing.google_workspace.user`: Cost per Google Workspace user

### Feature Flags
- `features.intelligent_alert_correlation`: Enable/disable alert correlation
- `features.predictive_risk_assessment`: Enable/disable risk prediction
- `features.saas_license_optimization`: Enable/disable license optimization
- `features.screenshot_verification`: Enable/disable screenshot features
- `features.interactive_qbr`: Enable/disable QBR generation
- `features.business_impact_calculations`: Enable/disable cost calculations

## 🚨 Error Handling

### Common Error Responses

**Configuration Error:**
```json
{
  "error": "Feature disabled in configuration",
  "feature": "intelligent_alert_correlation",
  "suggestion": "Enable in partner_config.yaml features section"
}
```

**API Authentication Error:**
```json
{
  "error": "Datto API authentication failed",
  "details": "Invalid API credentials",
  "suggestion": "Verify DATTO_PUBLIC_KEY and DATTO_SECRET_KEY environment variables"
}
```

**Rate Limit Error:**
```json
{
  "error": "API rate limit exceeded",
  "remaining_requests": 0,
  "reset_time": "2024-01-15T16:00:00Z",
  "suggestion": "Wait for rate limit reset or contact Datto support"
}
```

**Missing Data Error:**
```json
{
  "error": "Insufficient data for analysis",
  "details": "No devices found or API returned empty response",
  "suggestion": "Verify devices are properly configured in Datto Partner Portal"
}
```

## 🔍 Debugging

### Verbose Logging
Enable detailed logging by setting configuration:
```yaml
logging:
  level: "DEBUG"
```

### API Request Tracing
Monitor API calls and performance:
```python
# Check rate limit status
health_check = system_health_check()
print(f"Rate limit remaining: {health_check['rate_limit_remaining']}")

# Check API connectivity
if health_check['api_status'] != 'healthy':
    print(f"API issue: {health_check.get('api_error', 'Unknown error')}")
```

### Configuration Validation
```python
# Validate partner configuration
partner_info = get_resource("datto://partner-info")
if not partner_info['features_enabled']['intelligent_alerts']:
    print("Alert correlation is disabled - check feature flags")
```

---

**For additional support, contact the Datto Partner Success Team or refer to the complete documentation.**