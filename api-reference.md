# API Reference

This document provides detailed information about all MCP tools and resources available in the Datto Partner Portal MCP Server.

## Overview

The MCP server provides tools for:
- **Backup Health Analysis**: Cross-platform health monitoring
- **Reporting**: Automated QBR generation and business impact calculations
- **Device Management**: BCDR device inventory and monitoring
- **Optimization**: SaaS license optimization and cost analysis
- **Analytics**: Predictive risk assessment and alert correlation

## Tools

### Backup Health & Monitoring

#### `analyze_backup_health()`

Analyze overall backup health across all Datto backup products.

**Returns:** `Dict[str, Any]`

```json
{
  "overall_health_score": 85.5,
  "health_status": "Good",
  "bcdr_health": {
    "total_devices": 12,
    "devices_with_alerts": 2,
    "total_protected_assets": 45,
    "health_score": 83.3
  },
  "dtc_health": {
    "total_assets": 25,
    "assets_with_recent_failures": 1,
    "total_protected_data_gb": 2500.5,
    "health_score": 95.0
  },
  "saas_health": {
    "total_domains": 5,
    "total_seats": 150,
    "active_seats": 145,
    "inactive_seats": 5,
    "total_protected_data_gb": 182.0,
    "health_score": 96.7
  },
  "recommendations": [
    "Address alerts on 2 BCDR devices",
    "Review 5 inactive SaaS seats for optimization"
  ],
  "timestamp": "2025-06-13T10:30:00"
}
```

**Health Score Calculation:**
- **BCDR**: Based on alert count vs total devices
- **DTC**: Based on recent backup failures
- **SaaS**: Based on active vs total seats
- **Overall**: Weighted average of active platforms

---

#### `list_bcdr_devices(page=1, per_page=50, show_hidden=False, show_child_reseller=False)`

List all BCDR devices with optional filters and pagination.

**Parameters:**
- `page` (int): Page number for pagination (default: 1)
- `per_page` (int): Items per page (default: from config, max: 100)
- `show_hidden` (bool): Include hidden devices (default: False)
- `show_child_reseller` (bool): Include child reseller devices (default: False)

**Returns:** `Dict[str, Any]`

```json
{
  "items": [
    {
      "serialNumber": "ABC123DEF456",
      "name": "CLIENT-SIRIS-01",
      "model": "S5-2",
      "alertCount": 0,
      "agentCount": 5,
      "shareCount": 2,
      "lastSeenDate": "2025-06-13T08:00:00Z",
      "organizationName": "Example Client"
    }
  ],
  "summary": {
    "total_devices": 12,
    "devices_online": 11,
    "devices_with_alerts": 1,
    "total_agents": 45,
    "total_shares": 12
  }
}
```

---

### Business Impact & Reporting

#### `calculate_business_impact(recovery_type, downtime_hours, employees_affected, ransom_demand=None)`

Calculate business impact costs for recovery scenarios using configured rates.

**Parameters:**
- `recovery_type` (str): Type of incident ("ransomware", "hardware_failure", "human_error", "software_corruption", "natural_disaster", "cyber_attack")
- `downtime_hours` (float): Duration of downtime in hours
- `employees_affected` (int): Number of employees impacted
- `ransom_demand` (float, optional): Ransom amount for ransomware incidents

**Returns:** `Dict[str, Any]`

**Example - Ransomware:**
```json
{
  "recovery_type": "Ransomware Attack",
  "ransom_demand": "$100,000.00",
  "total_ransomware_cost": "$462,000.00",
  "downtime_cost": "$75,000.00",
  "total_business_impact": "$537,000.00",
  "calculation_notes": [
    "Ransom demand: $100,000.00",
    "Total ransomware costs (configured multiplier): $462,000.00",
    "Downtime costs (24 hours): $75,000.00",
    "Total business impact avoided by backup recovery"
  ]
}
```

**Example - Hardware Failure:**
```json
{
  "recovery_type": "Hardware Failure",
  "downtime_hours": 4.0,
  "employees_affected": 50,
  "downtime_cost": "$18,500.00",
  "calculation_notes": [
    "Business impact based on 50 employees affected",
    "Downtime cost calculation: 4.0 hours using configured rates",
    "Total cost avoided through successful backup recovery"
  ]
}
```

**Calculation Method:**
- **Employee Cost**: `employees_affected × hourly_cost × productivity_loss × downtime_hours`
- **IT Response**: `it_staff_hourly_cost × average_incident_response_hours`
- **Ransomware Multiplier**: `ransom_demand × configured_multiplier` (default: 4.62x)

---

### QBR Generation

#### `create_qbr(client_name, msp_name=None, quarter=None, recovery_events=None, employee_count=None)`

Create a comprehensive Quarterly Business Review report with real API data.

**Parameters:**
- `client_name` (str): Name of the client
- `msp_name` (str, optional): MSP company name
- `quarter` (str, optional): Quarter for report (e.g., "Q2 2025")
- `recovery_events` (str, optional): JSON string of recovery events
- `employee_count` (int, optional): Number of employees for calculations

**Returns:** `str` - Success message with file path

**Recovery Events Format:**
```json
[
  {
    "date": "2025-05-15",
    "type": "ransomware",
    "systems": ["File Server", "Domain Controller"],
    "downtime_hours": 12.0,
    "employees_affected": 50,
    "ransom_demand": 100000,
    "recovery_successful": true,
    "data_loss_gb": 0,
    "recovery_time_minutes": 180,
    "description": "Ransomware attack on file server"
  }
]
```

**Generated Report Includes:**
- Executive summary with key metrics
- Cross-platform backup health analysis
- Business impact calculations
- Screenshot verification gallery
- BCDR appliance health and capacity
- SaaS protection statistics
- Recovery event analysis
- Strategic recommendations
- ROI calculations

---

### Advanced Analytics

#### `predict_backup_risks()`

ML-based prediction of likely backup failures in next 24-48 hours.

**Returns:** `RiskAssessment`

```json
{
  "high_risk_assets": [
    {
      "asset_id": "ABC123_agent_001",
      "asset_name": "CLIENT-DC01/Exchange Server",
      "risk_score": 85.0,
      "risk_factors": [
        "No backup for 36 hours",
        "Screenshot verification failing",
        "Device has 3 alerts"
      ],
      "predicted_failure_window": "Next 4-8 hours",
      "recommended_actions": [
        "Immediate investigation required",
        "Check backup infrastructure"
      ],
      "historical_pattern": "Backup timing issues"
    }
  ],
  "medium_risk_assets": [...],
  "low_risk_assets": [...],
  "overall_risk_score": 25.5,
  "predictions": [
    "3 assets likely to fail within 8 hours",
    "5 assets at elevated risk in next 24 hours",
    "Primary risk pattern: Screenshot verification (3 assets)"
  ],
  "preventive_actions": [
    "Immediately investigate 3 high-risk assets",
    "Fix screenshot verification on 3 assets",
    "Schedule proactive maintenance window for at-risk systems"
  ],
  "timestamp": "2025-06-13T10:30:00"
}
```

**Risk Factors Analyzed:**
- Backup age and frequency
- Screenshot verification status
- Device alert counts
- Historical failure patterns
- Asset pause status

---

#### `correlate_alerts()`

AI-powered alert correlation across all devices to reduce noise and identify patterns.

**Returns:** `AlertAnalysis`

```json
{
  "total_alerts": 25,
  "correlations": [
    {
      "correlation_id": "1",
      "alert_count": 8,
      "affected_devices": ["DEV001", "DEV002", "DEV003"],
      "common_timeframe": "14:30 - 16:45",
      "probable_root_cause": "Network infrastructure issue at Main Office",
      "suggested_actions": [
        "Check network connectivity and firewall rules",
        "Verify internet connection at affected locations"
      ],
      "severity": "critical",
      "first_occurrence": "2025-06-13T14:30:00",
      "last_occurrence": "2025-06-13T16:45:00"
    }
  ],
  "isolated_alerts": [...],
  "noise_reduction_percentage": 68.0,
  "actionable_items": [
    "URGENT: Network infrastructure issue affecting 3 devices",
    "Investigate 2 alert patterns for systemic issues"
  ],
  "timestamp": "2025-06-13T10:30:00"
}
```

**Correlation Logic:**
- Groups alerts within 4-hour windows
- Identifies common patterns across devices
- Suggests root causes based on alert types
- Provides actionable remediation steps

---

#### `optimize_saas_licensing()`

Analyze and optimize SaaS Protection licensing for cost savings.

**Returns:** `LicenseOptimization`

```json
{
  "total_seats": 150,
  "active_seats": 142,
  "inactive_seats": 8,
  "wasted_licenses": [
    {
      "seat_id": "user123",
      "seat_name": "example.com/<EMAIL>",
      "seat_type": "User",
      "days_inactive": 120,
      "potential_monthly_savings": 4.00,
      "recommendation": "Review inactive user, consider pausing"
    }
  ],
  "total_monthly_waste": 32.00,
  "optimization_opportunities": [
    "Review 8 inactive User seats for optimization",
    "Focus on User seats - highest waste potential ($28/month)"
  ],
  "auto_actions": [
    "Auto-pause 5 high-value inactive seats",
    "Auto-remove 2 long-inactive seats"
  ],
  "timestamp": "2025-06-13T10:30:00"
}
```

**Optimization Criteria:**
- Seats inactive for configured threshold days
- Paused or archived seats still being billed
- High-value seats with potential savings
- Cost analysis based on configured pricing

---

## Resources

### `datto://screenshots`

Get all recent screenshots from assets that support screenshot verification.

**Returns:** `List[Dict[str, Any]]`

```json
[
  {
    "name": "CLIENT-DC01",
    "url": "https://device.dattobackup.com/sirisReporting/images/latest/abc123.png",
    "success": true,
    "device": "CLIENT-SIRIS-01",
    "timestamp": "06/13 08:02",
    "type": "BCDR Agent"
  },
  {
    "name": "CLIENT-FS01",
    "url": "https://device.dattobackup.com/sirisReporting/images/latest/def456.png", 
    "success": false,
    "device": "CLIENT-SIRIS-02",
    "timestamp": "06/13 07:45",
    "type": "BCDR Agent"
  }
]
```

---

### `datto://screenshots/{asset_type}`

Get screenshots filtered by asset type.

**Parameters:**
- `asset_type`: "bcdr-agent" or "dtc-server"

**Returns:** Filtered list of screenshots matching the specified type.

---

## Configuration Impact on APIs

Many API responses are influenced by configuration settings:

### Business Impact Calculations

Controlled by `qbr.business_impact` section:
- `employee_hourly_cost`: Base hourly cost for employees
- `it_staff_hourly_cost`: IT staff response cost
- `ransomware_cost_multiplier`: Total ransomware cost multiplier
- `productivity_loss_multiplier`: Productivity impact during outages

### Performance Limits

Controlled by `performance` section:
- `max_devices_per_query`: Limits device queries
- `max_assets_per_device`: Limits asset analysis depth
- `screenshot_limit`: Screenshots in dashboards
- `backup_history_days`: Historical backup analysis range

### Health Scoring

Controlled by `reports.health_scoring`:
- `excellent_threshold`: Score for "Excellent" status
- `good_threshold`: Score for "Good" status
- `high_risk_threshold`: Risk classification threshold

## Error Handling

All APIs include comprehensive error handling:

### Common Error Responses

```json
{
  "error": "Failed to analyze backup health: Connection timeout",
  "overall_health_score": 0,
  "health_status": "Unknown",
  "recommendations": ["Unable to analyze - check API connectivity"],
  "timestamp": "2025-06-13T10:30:00"
}
```

### Rate Limiting

When rate limits are approached:
- APIs automatically reduce query sizes
- Graceful degradation with partial data
- Error messages indicate rate limit issues

### Network Issues

APIs handle network issues gracefully:
- Automatic retries for transient failures
- Partial data collection when some endpoints fail
- Clear error messages for connectivity issues

## Best Practices

### API Usage

1. **Monitor rate limits**: Check rate_limit_remaining in client
2. **Handle errors gracefully**: All APIs can return partial data
3. **Configure timeouts appropriately**: Based on network conditions
4. **Use filtering**: Reduce data volume with parameters

### Performance

1. **Adjust query limits**: For large environments
2. **Cache results**: When possible for frequently accessed data
3. **Monitor resource usage**: CPU and memory during heavy operations
4. **Schedule intensive operations**: During off-peak hours

### Security

1. **Protect API credentials**: Never log or expose keys
2. **Use HTTPS**: All API communication is encrypted
3. **Validate inputs**: Especially for user-provided data
4. **Monitor access**: Log API usage for security auditing