#!/usr/bin/env python3
"""
Data models for Datto Partner MCP Server
Pydantic models for API responses, configurations, and business logic.
"""

from datetime import datetime
from typing import List, Dict, Any, Optional
from enum import Enum
from pydantic import BaseModel, Field

# ============================================================================
# Alert Models
# ============================================================================

class AlertSeverity(str, Enum):
    """Alert severity levels."""
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"

class AlertCorrelation(BaseModel):
    """Correlated alert analysis."""
    correlation_id: str
    alert_count: int
    affected_devices: List[str]
    common_timeframe: str
    probable_root_cause: str
    suggested_actions: List[str]
    severity: AlertSeverity
    first_occurrence: datetime
    last_occurrence: datetime

class AlertAnalysis(BaseModel):
    """Comprehensive alert analysis."""
    total_alerts: int
    correlations: List[AlertCorrelation]
    isolated_alerts: List[Dict[str, Any]]
    noise_reduction_percentage: float
    actionable_items: List[str]
    timestamp: datetime

# ============================================================================
# Risk Assessment Models
# ============================================================================

class BackupRisk(BaseModel):
    """Backup failure risk assessment."""
    asset_id: str
    asset_name: str
    risk_score: float  # 0-100
    risk_factors: List[str]
    predicted_failure_window: str
    recommended_actions: List[str]
    historical_pattern: str

class RiskAssessment(BaseModel):
    """Predictive backup failure analysis."""
    high_risk_assets: List[BackupRisk]
    medium_risk_assets: List[BackupRisk]
    low_risk_assets: List[BackupRisk]
    overall_risk_score: float
    predictions: List[str]
    preventive_actions: List[str]
    timestamp: datetime

# ============================================================================
# License Optimization Models
# ============================================================================

class LicenseWaste(BaseModel):
    """License waste identification."""
    seat_id: str
    seat_name: str
    seat_type: str
    days_inactive: int
    potential_monthly_savings: float
    recommendation: str

class LicenseOptimization(BaseModel):
    """SaaS license optimization analysis."""
    total_seats: int
    active_seats: int
    inactive_seats: int
    wasted_licenses: List[LicenseWaste]
    total_monthly_waste: float
    optimization_opportunities: List[str]
    auto_actions: List[str]
    timestamp: datetime

# ============================================================================
# QBR Models
# ============================================================================

class RecoveryType(str, Enum):
    """Types of recovery events."""
    RANSOMWARE = "ransomware"
    HARDWARE_FAILURE = "hardware_failure"
    HUMAN_ERROR = "human_error"
    NATURAL_DISASTER = "natural_disaster"
    CYBER_ATTACK = "cyber_attack"
    SOFTWARE_CORRUPTION = "software_corruption"

class RecoveryEvent(BaseModel):
    """Recovery event details."""
    event_date: datetime
    recovery_type: RecoveryType
    affected_systems: List[str]
    downtime_hours: float
    employees_affected: int
    ransom_demand: Optional[float] = None
    recovery_successful: bool = True
    data_loss_gb: float = 0
    recovery_time_minutes: int
    business_impact_description: str

class QBRMetrics(BaseModel):
    """QBR metrics and calculations."""
    client_name: str
    quarter: str
    total_devices: int
    total_users: int
    total_data_protected_gb: float
    uptime_percentage: float
    successful_backups: int
    failed_backups: int
    recovery_events: List[RecoveryEvent]
    monthly_service_cost: float
    
    # Calculated values
    downtime_cost_avoided: float = 0
    ransom_cost_avoided: float = 0
    productivity_cost_avoided: float = 0
    compliance_value: float = 0
    total_roi: float = 0

# ============================================================================
# Dashboard Models
# ============================================================================

class HealthStatus(BaseModel):
    """System health status."""
    overall_score: float
    status: str  # Excellent, Good, Fair, Poor
    devices_online: int
    devices_with_alerts: int
    total_protected_assets: int
    recommendations: List[str]
    timestamp: datetime

class Screenshot(BaseModel):
    """Screenshot verification data."""
    name: str
    url: str
    success: bool
    device: str
    timestamp: str
    asset_type: str  # BCDR Agent, DTC Server, etc.

class DashboardData(BaseModel):
    """Executive dashboard data."""
    health_status: HealthStatus
    alert_analysis: AlertAnalysis
    risk_assessment: RiskAssessment
    license_optimization: LicenseOptimization
    screenshots: List[Screenshot]
    partner_info: Dict[str, Any]
    generated_at: datetime

# ============================================================================
# API Response Models
# ============================================================================

class BCDRDevice(BaseModel):
    """BCDR device information."""
    serial_number: str
    name: str
    model: str
    organization_name: str
    last_seen_date: Optional[str]
    alert_count: int
    agent_count: int
    share_count: int
    status: str

class SaaSCustomer(BaseModel):
    """SaaS customer information."""
    customer_id: str
    domain: str
    product_type: str
    total_seats: int
    active_seats: int
    billing_status: str

class Asset(BaseModel):
    """Protected asset information."""
    asset_id: str
    name: str
    asset_type: str
    last_backup: Optional[datetime]
    backup_status: str
    data_size_gb: float
    screenshot_url: Optional[str]
    screenshot_success: Optional[bool]

# ============================================================================
# Business Impact Models
# ============================================================================

class BusinessImpactCalculation(BaseModel):
    """Business impact cost calculation."""
    recovery_type: str
    downtime_hours: float
    employees_affected: int
    downtime_cost: float
    ransom_demand: Optional[float] = None
    total_ransomware_cost: Optional[float] = None
    total_business_impact: float
    calculation_notes: List[str]
    cost_model_used: str  # small_business, mid_market, enterprise

class ROIAnalysis(BaseModel):
    """Return on investment analysis."""
    quarterly_investment: float
    total_value_delivered: float
    roi_percentage: float
    downtime_avoided: float
    compliance_value: float
    cost_savings_breakdown: Dict[str, float]
    
# ============================================================================
# Recommendation Models
# ============================================================================

class MSPRecommendation(BaseModel):
    """MSP upselling recommendation."""
    priority: str  # High Priority, Medium Priority, Strategic, etc.
    title: str
    description: str
    value_proposition: str
    estimated_revenue: Optional[float] = None
    implementation_effort: str  # Low, Medium, High
    category: str  # Security, Infrastructure, Compliance, etc.

class RecommendationSet(BaseModel):
    """Set of MSP recommendations."""
    recommendations: List[MSPRecommendation]
    total_opportunity_value: float
    quick_wins: List[MSPRecommendation]
    strategic_initiatives: List[MSPRecommendation]
    generated_for: str  # Client name
    generated_by: str  # Partner name
    timestamp: datetime