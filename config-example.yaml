# Datto Partner Portal MCP Configuration Template
# Copy this file to dpp-config.yaml and customize for your environment

# API Configuration - REQUIRED
api:
  public_key: "YOUR_DATTO_PUBLIC_KEY_HERE"
  secret_key: "YOUR_DATTO_SECRET_KEY_HERE"
  base_url: "https://api.datto.com"
  timeout_seconds: 60
  rate_limit_budget: 10000

# Performance Settings
performance:
  max_devices_per_query: 50
  max_assets_per_device: 10
  max_dtc_assets: 50
  max_saas_domains: 20
  screenshot_limit: 8
  backup_history_days: 30
  backup_history_limit: 100

# QBR Business Impact Calculations - Customize for your market
qbr:
  business_impact:
    # Average hourly employee cost in your market
    employee_hourly_cost: 75.00
    
    # Productivity loss during outages (0.0-1.0)
    productivity_loss_multiplier: 0.85
    
    # Your IT staff hourly billing rate
    it_staff_hourly_cost: 125.00
    
    # Average time spent on incidents
    average_incident_response_hours: 4.0
    
    # Ransomware total cost multiplier (IBM Security: 4.62x average)
    ransomware_cost_multiplier: 4.62
    
    # Industry average ransom demand (FBI: $220k)
    default_ransom_demand: 220000.00

  recovery_times:
    # Adjust based on your typical recovery times (minutes)
    hardware_failure: 240    # 4 hours
    ransomware: 720         # 12 hours  
    human_error: 60         # 1 hour
    software_corruption: 180 # 3 hours
    natural_disaster: 1440   # 24 hours
    cyber_attack: 480       # 8 hours

  storage_costs:
    # Your storage pricing for ROI calculations
    cost_per_gb_month: 0.12
    deduplication_ratio: 3.5
    alternative_storage_cost_multiplier: 1.8

  compliance:
    # Average compliance violation costs (adjust for your industry)
    hipaa_violation_average: 2300000
    pci_violation_average: 190000
    gdpr_violation_average: 4800000
    sox_violation_average: 1500000

# Report Customization - Update with your MSP details
reports:
  msp_branding:
    default_msp_name: "Your MSP Name Here"
    
    name_expansions:
      CED: "CED Technology Solutions"
      # Add your MSP abbreviations here
  
  contact_info:
    support_email: "<EMAIL>"
    support_phone: "(*************"
    website: "https://www.yourmsp.com"
  
  health_scoring:
    excellent_threshold: 90
    good_threshold: 75
    needs_attention_threshold: 60
    high_risk_threshold: 70
    medium_risk_threshold: 40

# SaaS License Optimization - Update pricing for your market
saas:
  pricing:
    office365:
      user: 4.00
      shared_mailbox: 2.00
      site: 1.00
      team_site: 1.00
      team: 1.00
    google_workspace:
      user: 3.00
      shared_drive: 1.00
      team: 1.00
    default_seat_cost: 2.00
  
  optimization:
    inactive_threshold_days: 90
    removal_threshold_days: 180
    auto_action_savings_threshold: 25.00
    optimization_threshold_seats: 10

# Alert Correlation
alerts:
  correlation:
    correlation_window_hours: 4
    min_correlation_size: 2
    critical_threshold_devices: 3

# Infrastructure Analysis
infrastructure:
  capacity:
    storage_warning_threshold: 80
    server_age_threshold: 5
    single_link_warning: true

# Artifact Management
artifacts:
  output_directory: "artifacts"
  filename_format: "{client_name}_QBR_{quarter}_{date}"
  keep_previous_versions: 5
  max_report_size: 50
  max_screenshot_size: 5

# Logging
logging:
  level: "INFO"
  file: "logs/dpp-mcp.log"
  max_size_mb: 100
  backup_count: 5