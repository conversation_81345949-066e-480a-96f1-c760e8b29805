#!/usr/bin/env python3
"""
Test suite for Datto Partner MCP Server
Comprehensive tests for all major components.
"""

import pytest
import asyncio
import os
import sys
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from config import PartnerConfig, PartnerInfo, DattoConfig
from models import AlertAnalysis, AlertCorrelation, AlertSeverity
from utils import (
    validate_api_credentials, 
    format_currency, 
    calculate_business_size_category,
    validate_email,
    validate_phone
)

class TestConfig:
    """Test configuration management."""
    
    def test_partner_info_validation(self):
        """Test partner info validation."""
        # Valid partner info
        partner = PartnerInfo(
            name="Test MSP",
            initials="TST",
            support_email="<EMAIL>",
            emergency_phone="1-800-TEST-MSP"
        )
        assert partner.name == "Test MSP"
        assert partner.initials == "TST"
    
    def test_datto_config_validation(self):
        """Test Datto API config validation."""
        config = DattoConfig(
            public_key="test-public-key",
            secret_key="test-secret-key"
        )
        assert config.base_url == "https://api.datto.com"
        assert config.rate_limit_budget == 10000

class TestUtils:
    """Test utility functions."""
    
    def test_validate_api_credentials(self):
        """Test API credential validation."""
        # Valid credentials
        assert validate_api_credentials("valid-public-key", "valid-secret-key") == True
        
        # Invalid credentials
        assert validate_api_credentials("", "") == False
        assert validate_api_credentials("your_public_key", "your_secret_key") == False
        assert validate_api_credentials("${DATTO_PUBLIC_KEY}", "${DATTO_SECRET_KEY}") == False
        assert validate_api_credentials("short", "short") == False
    
    def test_format_currency(self):
        """Test currency formatting."""
        assert format_currency(1234.56) == "$1,235"
        assert format_currency(1234.56, include_cents=True) == "$1,234.56"
        assert format_currency(1000000) == "$1,000,000"
    
    def test_business_size_category(self):
        """Test business size categorization."""
        assert calculate_business_size_category(50) == "small_business"
        assert calculate_business_size_category(500) == "mid_market"
        assert calculate_business_size_category(2000) == "enterprise"
    
    def test_email_validation(self):
        """Test email validation."""
        assert validate_email("<EMAIL>") == True
        assert validate_email("invalid-email") == False
        assert validate_email("test@") == False
        assert validate_email("@example.com") == False
    
    def test_phone_validation(self):
        """Test phone number validation."""
        assert validate_phone("**************") == True
        assert validate_phone("(*************") == True
        assert validate_phone("5551234567") == True
        assert validate_phone("123") == False  # Too short
        assert validate_phone("123456789012345678") == False  # Too long

class TestModels:
    """Test data models."""
    
    def test_alert_correlation_model(self):
        """Test alert correlation model."""
        correlation = AlertCorrelation(
            correlation_id="test-123",
            alert_count=5,
            affected_devices=["device1", "device2"],
            common_timeframe="09:00 - 10:00",
            probable_root_cause="Network connectivity issue",
            suggested_actions=["Check network", "Contact support"],
            severity=AlertSeverity.CRITICAL,
            first_occurrence=datetime.now(),
            last_occurrence=datetime.now()
        )
        
        assert correlation.correlation_id == "test-123"
        assert correlation.alert_count == 5
        assert correlation.severity == AlertSeverity.CRITICAL
    
    def test_alert_analysis_model(self):
        """Test alert analysis model."""
        analysis = AlertAnalysis(
            total_alerts=10,
            correlations=[],
            isolated_alerts=[],
            noise_reduction_percentage=75.0,
            actionable_items=["Contact support"],
            timestamp=datetime.now()
        )
        
        assert analysis.total_alerts == 10
        assert analysis.noise_reduction_percentage == 75.0

@pytest.fixture
def mock_config():
    """Create mock configuration for testing."""
    return PartnerConfig(
        partner=PartnerInfo(
            name="Test MSP",
            initials="TST",
            support_email="<EMAIL>",
            emergency_phone="1-800-TEST-MSP"
        ),
        datto=DattoConfig(
            public_key="test-public-key",
            secret_key="test-secret-key"
        )
    )

@pytest.fixture
def mock_client():
    """Create mock HTTP client for testing."""
    client = Mock()
    client.get = AsyncMock()
    client.put = AsyncMock()
    return client

class TestDattoMCP:
    """Test main MCP functionality."""
    
    @pytest.mark.asyncio
    async def test_list_bcdr_devices_validation(self, mock_config, mock_client):
        """Test BCDR device listing with input validation."""
        from datto_mcp import list_bcdr_devices
        
        # Test invalid page number
        with pytest.raises(ValueError, match="Page number must be >= 1"):
            await list_bcdr_devices(page=0)
        
        # Test invalid per_page
        with pytest.raises(ValueError, match="per_page must be between 1 and 1000"):
            await list_bcdr_devices(per_page=0)
        
        with pytest.raises(ValueError, match="per_page must be between 1 and 1000"):
            await list_bcdr_devices(per_page=1001)
    
    @pytest.mark.asyncio
    async def test_analyze_backup_health(self, mock_config, mock_client):
        """Test backup health analysis."""
        # Mock device data
        mock_client.get.return_value = {
            "items": [
                {"serialNumber": "123", "name": "Device1", "alertCount": 0},
                {"serialNumber": "456", "name": "Device2", "alertCount": 2}
            ]
        }
        
        # This would require importing and mocking the actual function
        # For now, we'll test the logic components
        devices_with_alerts = 1
        total_devices = 2
        alert_ratio = devices_with_alerts / total_devices
        health_score = max(0, 100 - (alert_ratio * 100))
        
        assert health_score == 50.0  # 50% health score

class TestIntegration:
    """Integration tests."""
    
    @pytest.mark.asyncio
    async def test_configuration_loading(self):
        """Test configuration loading from file."""
        # This would test actual config file loading
        # For now, we'll test the validation logic
        pass
    
    def test_environment_variable_substitution(self):
        """Test environment variable substitution in config."""
        import re
        
        def replace_env_vars(match):
            var_name = match.group(1)
            return os.getenv(var_name, match.group(0))
        
        yaml_content = "key: ${TEST_VAR}"
        os.environ["TEST_VAR"] = "test_value"
        
        result = re.sub(r'\$\{([^}]+)\}', replace_env_vars, yaml_content)
        assert result == "key: test_value"

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
