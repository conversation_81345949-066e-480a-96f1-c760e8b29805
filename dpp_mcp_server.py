#!/usr/bin/env python3
"""
Datto Partner Portal MCP Server - Partner Ready Edition
Comprehensive MCP server for Datto Partner Portal API integration with real-time monitoring,
automated reporting, and cross-platform backup analytics.
"""

import os
import sys
import yaml
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from enum import Enum
from collections import defaultdict, Counter
import statistics

import httpx
from pydantic import BaseModel, Field
from fastmcp import FastMCP

# Import local modules
from utils.api_client import DattoClient, DattoConfig
from utils.formatting import *
from qbr.metrics import BusinessImpactCalculator, QBRMetrics, RecoveryEvent, RecoveryType
from qbr.generator import QBRGenerator

# Configuration loading
def load_config() -> Dict[str, Any]:
    """Load configuration from YAML file."""
    config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'dpp-config.yaml')
    
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Expand environment variables
    api_config = config.get('api', {})
    for key in ['public_key', 'secret_key']:
        value = api_config.get(key, '')
        if value.startswith('${') and value.endswith('}'):
            env_var = value[2:-1]
            api_config[key] = os.getenv(env_var, '')
    
    return config

# Initialize configuration and clients
try:
    CONFIG = load_config()
except Exception as e:
    print(f"Error loading configuration: {e}")
    print("Please ensure config/dpp-config.yaml exists and is properly configured.")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=getattr(logging, CONFIG.get('logging', {}).get('level', 'INFO')),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize FastMCP
mcp = FastMCP("datto-partner-portal")

# Initialize API client
try:
    api_config = CONFIG.get('api', {})
    datto_config = DattoConfig(
        public_key=api_config.get('public_key', ''),
        secret_key=api_config.get('secret_key', ''),
        base_url=api_config.get('base_url', 'https://api.datto.com'),
        timeout_seconds=api_config.get('timeout_seconds', 60),
        rate_limit_budget=api_config.get('rate_limit_budget', 10000)
    )
    client = DattoClient(datto_config)
except Exception as e:
    logger.error(f"Failed to initialize Datto API client: {e}")
    sys.exit(1)

# Initialize business impact calculator and QBR generator
business_calculator = BusinessImpactCalculator(CONFIG)
qbr_generator = QBRGenerator(client, CONFIG)

# ============================================================================
# Enhanced Models for Advanced Features
# ============================================================================

class AlertSeverity(str, Enum):
    """Alert severity levels."""
    CRITICAL = "critical"
    WARNING = "warning"
    INFO = "info"

class AlertCorrelation(BaseModel):
    """Correlated alert analysis."""
    correlation_id: str
    alert_count: int
    affected_devices: List[str]
    common_timeframe: str
    probable_root_cause: str
    suggested_actions: List[str]
    severity: AlertSeverity
    first_occurrence: datetime
    last_occurrence: datetime

class AlertAnalysis(BaseModel):
    """Comprehensive alert analysis."""
    total_alerts: int
    correlations: List[AlertCorrelation]
    isolated_alerts: List[Dict[str, Any]]
    noise_reduction_percentage: float
    actionable_items: List[str]
    timestamp: datetime

class BackupRisk(BaseModel):
    """Backup failure risk assessment."""
    asset_id: str
    asset_name: str
    risk_score: float  # 0-100
    risk_factors: List[str]
    predicted_failure_window: str
    recommended_actions: List[str]
    historical_pattern: str

class RiskAssessment(BaseModel):
    """Predictive backup failure analysis."""
    high_risk_assets: List[BackupRisk]
    medium_risk_assets: List[BackupRisk]
    low_risk_assets: List[BackupRisk]
    overall_risk_score: float
    predictions: List[str]
    preventive_actions: List[str]
    timestamp: datetime

class LicenseWaste(BaseModel):
    """License waste identification."""
    seat_id: str
    seat_name: str
    seat_type: str
    days_inactive: int
    potential_monthly_savings: float
    recommendation: str

class LicenseOptimization(BaseModel):
    """SaaS license optimization analysis."""
    total_seats: int
    active_seats: int
    inactive_seats: int
    wasted_licenses: List[LicenseWaste]
    total_monthly_waste: float
    optimization_opportunities: List[str]
    auto_actions: List[str]
    timestamp: datetime

# ============================================================================
# Core API Functions
# ============================================================================

async def _list_bcdr_devices_impl(
    page: int = 1,
    per_page: int = None,
    show_hidden: bool = False,
    show_child_reseller: bool = False
) -> Dict[str, Any]:
    """Implementation function for listing BCDR devices."""
    if per_page is None:
        per_page = CONFIG.get('performance', {}).get('max_devices_per_query', 50)
    
    params = {
        '_page': page,
        '_perPage': min(per_page, 100),
        'showHiddenDevices': 1 if show_hidden else 0,
        'showChildResellerDevices': 1 if show_child_reseller else 0
    }
    
    result = await client.get("/v1/bcdr/device", params=params)
    devices = result.get('items', [])
    
    # Calculate summary
    summary = {
        "total_devices": len(devices),
        "devices_online": len([d for d in devices if d.get('lastSeenDate')]),
        "devices_with_alerts": len([d for d in devices if d.get('alertCount', 0) > 0]),
        "total_agents": sum(d.get('agentCount', 0) for d in devices),
        "total_shares": sum(d.get('shareCount', 0) for d in devices)
    }
    
    result['summary'] = summary
    return result

async def _get_recent_screenshots() -> List[Dict[str, Any]]:
    """Get recent screenshots from assets that support screenshot verification."""
    screenshots = []
    max_screenshots = CONFIG.get('performance', {}).get('screenshot_limit', 8)
    
    try:
        # Get BCDR screenshots
        devices_result = await client.get("/v1/bcdr/device")
        devices = devices_result.get('items', [])
        
        for device in devices[:10]:  # Limit devices for performance
            serial = device.get('serialNumber')
            device_name = device.get('name', 'Unknown Device')
            
            try:
                agents_result = await client.get(f"/v1/bcdr/device/{serial}/asset/agent")
                agents = agents_result.get('items', [])
                
                for agent in agents[:3]:  # Max 3 agents per device
                    screenshot_url = agent.get('lastScreenshotUrl', '')
                    screenshot_status_raw = agent.get('lastScreenshotAttemptStatus', 0)
                    
                    # Parse screenshot status
                    if isinstance(screenshot_status_raw, bool):
                        screenshot_status = screenshot_status_raw
                    elif isinstance(screenshot_status_raw, (int, float)):
                        screenshot_status = screenshot_status_raw == 1
                    elif isinstance(screenshot_status_raw, str):
                        screenshot_status = screenshot_status_raw.lower() in ['true', '1', 'success']
                    else:
                        screenshot_status = False
                    
                    last_screenshot = agent.get('lastScreenshotAttempt', 0)
                    
                    if screenshot_url:
                        timestamp_value = last_screenshot if last_screenshot > 0 else agent.get('lastSnapshot', 0)
                        timestamp = format_timestamp(timestamp_value) if timestamp_value > 0 else 'No recent backup'
                        
                        screenshots.append({
                            'name': agent.get('name', 'Unknown Agent'),
                            'url': screenshot_url,
                            'success': screenshot_status,
                            'device': device_name,
                            'timestamp': timestamp,
                            'type': 'BCDR Agent'
                        })
            except Exception:
                continue
        
        # Get DTC screenshots
        try:
            dtc_result = await client.get("/v1/dtc/assets")
            dtc_assets = dtc_result.get('items', [])
            
            for dtc_asset in dtc_assets[:10]:
                if dtc_asset.get('type') == 'dtc-servers':
                    screenshot_url = dtc_asset.get('lastScreenshotUrl', '')
                    screenshot_success_raw = dtc_asset.get('screenshotSuccess', 0)
                    
                    if isinstance(screenshot_success_raw, bool):
                        screenshot_success = screenshot_success_raw
                    elif isinstance(screenshot_success_raw, (int, float)):
                        screenshot_success = screenshot_success_raw == 1
                    elif isinstance(screenshot_success_raw, str):
                        screenshot_success = screenshot_success_raw.lower() in ['true', '1', 'success']
                    else:
                        screenshot_success = False
                    
                    last_screenshot = dtc_asset.get('lastScreenshot', 0)
                    
                    if screenshot_url:
                        timestamp_value = last_screenshot if last_screenshot > 0 else dtc_asset.get('lastBackup', 0)
                        timestamp = format_timestamp(timestamp_value) if timestamp_value > 0 else 'No recent backup'
                        
                        screenshots.append({
                            'name': dtc_asset.get('name', 'Unknown Server'),
                            'url': screenshot_url,
                            'success': screenshot_success,
                            'device': dtc_asset.get('organizationName', 'DTC'),
                            'timestamp': timestamp,
                            'type': 'DTC Server'
                        })
        except Exception:
            pass
        
        # Balance screenshots (mix of success/failure)
        successful = [s for s in screenshots if s['success']]
        failed = [s for s in screenshots if not s['success']]
        
        successful.sort(key=lambda x: x['name'])
        failed.sort(key=lambda x: x['name'])
        
        balanced_screenshots = []
        max_len = max(len(successful), len(failed))
        for i in range(max_len):
            if i < len(successful):
                balanced_screenshots.append(successful[i])
            if i < len(failed):
                balanced_screenshots.append(failed[i])
        
        return balanced_screenshots[:max_screenshots]
        
    except Exception as e:
        logger.error(f"Error getting screenshots: {e}")
        return []

# ============================================================================
# MCP Tools - Backup Health and Analytics
# ============================================================================

@mcp.tool
async def analyze_backup_health() -> Dict[str, Any]:
    """Analyze overall backup health across all Datto backup products."""
    try:
        # Get BCDR health data
        devices = await _list_bcdr_devices_impl()
        
        total_devices = devices['summary']['total_devices']
        devices_with_alerts = devices['summary']['devices_with_alerts']
        
        bcdr_score = max(0, 100 - (devices_with_alerts / max(total_devices, 1) * 100)) if total_devices > 0 else 100
        
        # Get DTC health data
        dtc_health = {}
        try:
            dtc_result = await client.get("/v1/dtc/assets")
            dtc_assets = dtc_result.get('items', [])
            
            dtc_total = len(dtc_assets)
            dtc_failed_recent = 0
            dtc_total_data_gb = 0
            
            for asset in dtc_assets:
                last_10_backups = asset.get('last10Backups', [])
                if last_10_backups:
                    recent_failures = sum(1 for backup in last_10_backups[:3] if not backup.get('backupStatus', False))
                    if recent_failures >= 2:
                        dtc_failed_recent += 1
                
                protected_bytes = asset.get('protectedSizeBytes', 0)
                if protected_bytes > 0:
                    dtc_total_data_gb += protected_bytes / (1024**3)
            
            dtc_score = max(0, 100 - (dtc_failed_recent / max(dtc_total, 1) * 100)) if dtc_total > 0 else 100
            
            dtc_health = {
                "total_assets": dtc_total,
                "assets_with_recent_failures": dtc_failed_recent,
                "total_protected_data_gb": round(dtc_total_data_gb, 1),
                "health_score": round(dtc_score, 1)
            }
        except Exception:
            dtc_health = {"total_assets": 0, "health_score": 100, "error": "DTC API unavailable"}
        
        # Get SaaS health data
        saas_health = {}
        try:
            domains_result = await client.get("/v1/saas/domains")
            domains = domains_result.get('items', [])
            
            total_domains = len(domains)
            total_seats = 0
            active_seats = 0
            total_saas_data_gb = 0
            
            for domain in domains:
                saas_customer_id = domain.get('saasCustomerId')
                if saas_customer_id:
                    try:
                        seats_result = await client.get(f"/v1/saas/{saas_customer_id}/seats")
                        seats = seats_result.get('items', [])
                        total_seats += len(seats)
                        
                        for seat in seats:
                            if seat.get('seatState') == 'Active':
                                active_seats += 1
                        
                        apps_result = await client.get(f"/v1/saas/{saas_customer_id}/applications")
                        items = apps_result.get('items', [])
                        if items:
                            customer_used_bytes = items[0].get('usedBytes', 0)
                            if customer_used_bytes > 0:
                                total_saas_data_gb += customer_used_bytes / (1024**3)
                                
                    except Exception:
                        continue
            
            saas_score = (active_seats / max(total_seats, 1) * 100) if total_seats > 0 else 100
            
            saas_health = {
                "total_domains": total_domains,
                "total_seats": total_seats,
                "active_seats": active_seats,
                "inactive_seats": total_seats - active_seats,
                "total_protected_data_gb": round(total_saas_data_gb, 1),
                "health_score": round(saas_score, 1)
            }
        except Exception:
            saas_health = {"total_domains": 0, "total_seats": 0, "health_score": 100, "error": "SaaS API unavailable"}
        
        # Calculate overall health score
        health_scores = [bcdr_score]
        if dtc_health.get('total_assets', 0) > 0:
            health_scores.append(dtc_health['health_score'])
        if saas_health.get('total_seats', 0) > 0:
            health_scores.append(saas_health['health_score'])
        
        overall_score = sum(health_scores) / len(health_scores)
        
        # Generate recommendations
        recommendations = []
        if devices_with_alerts > 0:
            recommendations.append(f"Address alerts on {devices_with_alerts} BCDR devices")
        if dtc_health.get('assets_with_recent_failures', 0) > 0:
            recommendations.append(f"Investigate {dtc_health['assets_with_recent_failures']} DTC assets with recent backup failures")
        if saas_health.get('inactive_seats', 0) > 10:
            recommendations.append(f"Review {saas_health['inactive_seats']} inactive SaaS seats for optimization")
        
        if not recommendations:
            recommendations.append("All backup systems operating normally")
        
        return {
            "overall_health_score": round(overall_score, 2),
            "health_status": "Excellent" if overall_score >= 90 else "Good" if overall_score >= 75 else "Needs Attention",
            "bcdr_health": {
                "total_devices": total_devices,
                "devices_with_alerts": devices_with_alerts,
                "total_protected_assets": devices['summary']['total_agents'] + devices['summary']['total_shares'],
                "health_score": round(bcdr_score, 1)
            },
            "dtc_health": dtc_health,
            "saas_health": saas_health,
            "recommendations": recommendations,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to analyze backup health: {e}")
        return {
            "error": f"Failed to analyze backup health: {str(e)}",
            "overall_health_score": 0,
            "health_status": "Unknown",
            "bcdr_health": {"total_devices": 0, "devices_with_alerts": 0, "total_protected_assets": 0, "health_score": 0},
            "dtc_health": {"total_assets": 0, "health_score": 0},
            "saas_health": {"total_domains": 0, "total_seats": 0, "health_score": 0},
            "recommendations": ["Unable to analyze - check API connectivity"],
            "timestamp": datetime.now().isoformat()
        }

@mcp.tool
async def list_bcdr_devices(
    page: int = 1,
    per_page: int = None,
    show_hidden: bool = False,
    show_child_reseller: bool = False
) -> Dict[str, Any]:
    """List all BCDR devices (Siris, Alto, NAS) with optional filters."""
    return await _list_bcdr_devices_impl(page, per_page, show_hidden, show_child_reseller)

@mcp.tool
async def calculate_business_impact(
    recovery_type: str,
    downtime_hours: float,
    employees_affected: int,
    ransom_demand: Optional[float] = None
) -> Dict[str, Any]:
    """Calculate business impact costs for recovery scenarios using configured rates."""
    
    if recovery_type.lower() == 'ransomware':
        impact = business_calculator.calculate_ransomware_impact(
            ransom_demand or 0, downtime_hours, employees_affected
        )
        
        return {
            "recovery_type": "Ransomware Attack",
            "ransom_demand": format_currency(impact['ransom_demand']),
            "total_ransomware_cost": format_currency(impact['total_ransomware_cost']),
            "downtime_cost": format_currency(impact['downtime_cost']),
            "total_business_impact": format_currency(impact['total_impact']),
            "calculation_notes": [
                f"Ransom demand: {format_currency(impact['ransom_demand'])}",
                f"Total ransomware costs (configured multiplier): {format_currency(impact['total_ransomware_cost'])}",
                f"Downtime costs ({downtime_hours} hours): {format_currency(impact['downtime_cost'])}",
                "Total business impact avoided by backup recovery"
            ]
        }
    else:
        downtime_cost = business_calculator.calculate_downtime_cost(downtime_hours, employees_affected)
        
        return {
            "recovery_type": recovery_type.replace('_', ' ').title(),
            "downtime_hours": downtime_hours,
            "employees_affected": employees_affected,
            "downtime_cost": format_currency(downtime_cost),
            "calculation_notes": [
                f"Business impact based on {employees_affected} employees affected",
                f"Downtime cost calculation: {downtime_hours} hours using configured rates",
                "Total cost avoided through successful backup recovery"
            ]
        }

@mcp.tool
async def create_qbr(
    client_name: str,
    msp_name: Optional[str] = None,
    quarter: Optional[str] = None,
    recovery_events: Optional[str] = None,
    employee_count: Optional[int] = None
) -> str:
    """Create a comprehensive Quarterly Business Review report with real API data.
    
    Args:
        client_name: Name of the client for the QBR
        msp_name: MSP company name (defaults to config value)
        quarter: Quarter for the report (e.g., "Q2 2025")
        recovery_events: JSON string of recovery events for the quarter
        employee_count: Number of employees for business impact calculations
    
    Returns:
        Success message with the file path where the QBR was saved
    """
    
    try:
        # Parse recovery events if provided
        parsed_recovery_events = []
        if recovery_events:
            try:
                import json
                events_data = json.loads(recovery_events)
                parsed_recovery_events = events_data if isinstance(events_data, list) else []
            except json.JSONDecodeError:
                logger.warning("Invalid JSON format for recovery_events, proceeding without events")
        
        # Generate the QBR report
        result = await qbr_generator.generate_qbr(
            client_name=client_name,
            msp_name=msp_name,
            quarter=quarter,
            recovery_events=parsed_recovery_events,
            employee_count=employee_count
        )
        
        logger.info(f"QBR generated successfully for {client_name}")
        return result
        
    except Exception as e:
        error_msg = f"Failed to generate QBR: {str(e)}"
        logger.error(error_msg)
        return error_msg

# ============================================================================
# Resources
# ============================================================================

@mcp.resource("datto://screenshots")
async def screenshots_resource() -> List[Dict[str, Any]]:
    """Get all recent screenshots from assets that support screenshot verification."""
    return await _get_recent_screenshots()

@mcp.resource("datto://screenshots/{asset_type}")
async def screenshots_by_type(asset_type: str) -> List[Dict[str, Any]]:
    """Get screenshots filtered by asset type (bcdr-agent or dtc-server)."""
    all_screenshots = await _get_recent_screenshots()
    
    filter_map = {
        'bcdr-agent': 'BCDR Agent',
        'dtc-server': 'DTC Server'
    }
    
    target_type = filter_map.get(asset_type.lower())
    if target_type:
        return [s for s in all_screenshots if s['type'] == target_type]
    
    return all_screenshots

# ============================================================================
# Main Entry Point
# ============================================================================

if __name__ == "__main__":
    logger.info("Starting Datto Partner Portal MCP Server")
    logger.info(f"Configuration loaded from: {os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'config', 'dpp-config.yaml'))}")
    
    try:
        mcp.run()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
    finally:
        # Cleanup
        import asyncio
        asyncio.run(client.close())