<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{CLIENT_NAME} - Quarterly Business Review</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header Section */
        .qbr-header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .qbr-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 8px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        }

        .header-content {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 30px;
            align-items: center;
        }

        .client-info h1 {
            font-size: 3rem;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .qbr-subtitle {
            font-size: 1.4rem;
            color: #666;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .quarter-info {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }

        .info-item {
            background: linear-gradient(135deg, #f8f9ff, #e8f2ff);
            padding: 15px 25px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        .info-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .info-value {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-top: 5px;
        }

        .msp-logo {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            font-weight: 800;
            color: white;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        /* Executive Summary */
        .executive-summary {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }

        .section-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 1.5rem;
            color: white;
        }

        .section-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: #333;
        }

        .value-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .value-card {
            background: linear-gradient(135deg, #f8f9ff, #e8f2ff);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
            border: 2px solid transparent;
            background-clip: padding-box;
        }

        .value-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #28a745, #20c997);
        }

        .value-number {
            font-size: 3rem;
            font-weight: 800;
            color: #667eea;
            margin-bottom: 10px;
        }

        .value-label {
            font-size: 1.1rem;
            color: #666;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .value-description {
            font-size: 0.9rem;
            color: #888;
            line-height: 1.4;
        }

        /* Service Breakdown */
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 35px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .service-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 1.8rem;
            color: white;
        }

        .service-title {
            font-size: 1.6rem;
            font-weight: 700;
            color: #333;
        }

        .service-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9ff, #e8f2ff);
            border-radius: 15px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 800;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .performance-indicator {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .performance-text {
            font-weight: 600;
            color: #155724;
        }

        .performance-badge {
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .warning-indicator {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-left: 4px solid #ffc107;
        }

        .warning-indicator .performance-text {
            color: #856404;
        }

        .warning-indicator .performance-badge {
            background: #ffc107;
            color: #212529;
        }

        /* Asset Details */
        .asset-details {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 35px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .asset-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }

        .asset-item {
            background: linear-gradient(135d, #f8f9ff, #e8f2ff);
            border-radius: 15px;
            padding: 25px;
            border-left: 4px solid #667eea;
        }

        .asset-name {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .asset-type {
            font-size: 0.9rem;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .asset-metrics {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .metric-item {
            text-align: center;
        }

        .metric-value {
            font-size: 1.4rem;
            font-weight: 700;
            color: #667eea;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }

        /* ROI Section */
        .roi-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .roi-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .roi-content {
            position: relative;
            z-index: 1;
        }

        .roi-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .roi-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 15px;
        }

        .roi-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .roi-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
        }

        .roi-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .roi-value {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
        }

        .roi-label {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 600;
        }

        /* Recommendations */
        .recommendations {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 35px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .rec-item {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #ffc107;
        }

        .rec-priority {
            font-size: 0.85rem;
            font-weight: 700;
            color: #856404;
            text-transform: uppercase;
            margin-bottom: 10px;
        }

        .rec-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .rec-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .rec-value {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        /* Footer */
        .qbr-footer {
            text-align: center;
            padding: 30px;
            color: rgba(255, 255, 255, 0.8);
        }

        .footer-content {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container { padding: 15px; }
            .header-content { grid-template-columns: 1fr; text-align: center; }
            .client-info h1 { font-size: 2rem; }
            .service-grid { grid-template-columns: 1fr; }
            .value-metrics { grid-template-columns: 1fr; }
            .quarter-info { justify-content: center; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- QBR Header -->
        <div class="qbr-header">
            <div class="header-content">
                <div class="client-info">
                    <h1>{CLIENT_NAME}</h1>
                    <div class="qbr-subtitle">Quarterly Business Review - Backup & Recovery Services</div>
                    <div class="quarter-info">
                        <div class="info-item">
                            <div class="info-label">Review Period</div>
                            <div class="info-value">{QUARTER}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Service Focus</div>
                            <div class="info-value">BCDR & SaaS Protection</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Report Date</div>
                            <div class="info-value">{REPORT_DATE}</div>
                        </div>
                    </div>
                </div>
                <div class="msp-logo">{MSP_INITIALS}</div>
            </div>
        </div>

        <!-- Executive Summary -->
        <div class="executive-summary">
            <div class="section-header">
                <div class="section-icon">📊</div>
                <div class="section-title">Executive Summary</div>
            </div>
            
            <div class="value-metrics">
                <div class="value-card">
                    <div class="value-number">{UPTIME_PERCENTAGE}%</div>
                    <div class="value-label">Quarterly Uptime</div>
                    <div class="value-description">Exceeded 99% SLA target with minimal business disruption</div>
                </div>
                <div class="value-card">
                    <div class="value-number">{PROTECTED_USERS}</div>
                    <div class="value-label">Protected Users</div>
                    <div class="value-description">Complete SaaS backup coverage for all Office 365 accounts</div>
                </div>
                <div class="value-card">
                    <div class="value-number">{TOTAL_VALUE_DELIVERED}</div>
                    <div class="value-label">Estimated Value Delivered</div>
                    <div class="value-description">Risk mitigation and operational efficiency gains</div>
                </div>
                <div class="value-card">
                    <div class="value-number">{CRITICAL_RECOVERIES}</div>
                    <div class="value-label">Critical Recoveries</div>
                    <div class="value-description">Successful data restoration preventing business loss</div>
                </div>
            </div>

            <p style="font-size: 1.1rem; line-height: 1.8; color: #555; background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 4px solid #667eea;">
                <strong>Quarter Highlights:</strong> {CLIENT_NAME}'s backup infrastructure delivered exceptional value this quarter with {UPTIME_PERCENTAGE}% uptime, protecting {PROTECTED_USERS} users. {CRITICAL_RECOVERIES} critical data recovery incidents were resolved within SLA, preventing an estimated {DOWNTIME_AVOIDED} in business disruption costs. The implementation of enhanced monitoring has improved service reliability compared to the previous quarter.
            </p>
        </div>

        <!-- Service Breakdown -->
        <div class="service-grid">
            <!-- SaaS Backup Service -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">☁️</div>
                    <div class="service-title">SaaS Backup Protection</div>
                </div>
                
                <div class="performance-indicator {SAAS_STATUS_CLASS}">
                    <div class="performance-text">{SAAS_STATUS_TEXT}</div>
                    <div class="performance-badge">{SAAS_BADGE_TEXT}</div>
                </div>
                
                <div class="service-stats">
                    <div class="stat-item">
                        <div class="stat-value">{SAAS_USER_SEATS}</div>
                        <div class="stat-label">User Seats</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{SAAS_SERVICES_PROTECTED}</div>
                        <div class="stat-label">Services Protected</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{SAAS_SUCCESSFUL_BACKUPS}</div>
                        <div class="stat-label">Successful Backups</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{SAAS_DATA_PROTECTED}</div>
                        <div class="stat-label">Data Protected</div>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-top: 20px;">
                    <h4 style="color: #333; margin-bottom: 10px;">Protected Services Include:</h4>
                    <ul style="color: #666; line-height: 1.6;">
                        <li><strong>Exchange Online:</strong> 31 mailboxes with full email history</li>
                        <li><strong>SharePoint Online:</strong> 12 sites with document libraries</li>
                        <li><strong>OneDrive for Business:</strong> 31 personal drives</li>
                        <li><strong>Microsoft Teams:</strong> 6 teams with chat history</li>
                    </ul>
                </div>
            </div>

            <!-- BCDR Services -->
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">🛡️</div>
                    <div class="service-title">Business Continuity & DR</div>
                </div>
                
                <div class="performance-indicator {BCDR_STATUS_CLASS}">
                    <div class="performance-text">{BCDR_STATUS_TEXT}</div>
                    <div class="performance-badge">{BCDR_BADGE_TEXT}</div>
                </div>
                
                <div class="service-stats">
                    <div class="stat-item">
                        <div class="stat-value">{BCDR_PROTECTED_ASSETS}</div>
                        <div class="stat-label">Protected Assets</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{BCDR_DATA_VOLUME}</div>
                        <div class="stat-label">Data Volume</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">24/7</div>
                        <div class="stat-label">Monitoring</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{BCDR_RECOVERY_RTO}</div>
                        <div class="stat-label">Recovery RTO</div>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 12px; margin-top: 20px;">
                    <h4 style="color: #333; margin-bottom: 10px;">Critical Systems Protected:</h4>
                    <ul style="color: #666; line-height: 1.6;">
                        <li><strong>Domain Controller:</strong> Active Directory services</li>
                        <li><strong>File Server:</strong> Shared documents and resources</li>
                        <li><strong>Application Server:</strong> Business-critical applications</li>
                        <li><strong>Database Server:</strong> CRM and accounting data</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Asset Details -->
        <div class="asset-details">
            <div class="section-header">
                <div class="section-icon">💾</div>
                <div class="section-title">Protected Asset Portfolio</div>
            </div>
            
            <div class="asset-grid">
                {ASSET_PORTFOLIO}
            </div>
        </div>

        <!-- Screenshot Verification Section -->
        <div class="asset-details">
            <div class="section-header">
                <div class="section-icon">📸</div>
                <div class="section-title">Backup Verification Screenshots</div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-top: 25px;">
                {SCREENSHOT_GALLERY}
            </div>
        </div>

        <!-- ROI Section -->
        <div class="roi-section">
            <div class="roi-content">
                <div class="roi-header">
                    <div class="roi-title">Return on Investment</div>
                    <div class="roi-subtitle">Quantified Value Delivered This Quarter</div>
                </div>
                
                <div class="roi-metrics">
                    <div class="roi-card">
                        <div class="roi-value">{TOTAL_VALUE_DELIVERED}</div>
                        <div class="roi-label">Total Value Delivered</div>
                    </div>
                    <div class="roi-card">
                        <div class="roi-value">{QUARTERLY_INVESTMENT}</div>
                        <div class="roi-label">Quarterly Investment</div>
                    </div>
                    <div class="roi-card">
                        <div class="roi-value">{ROI_PERCENTAGE}%</div>
                        <div class="roi-label">ROI Percentage</div>
                    </div>
                    <div class="roi-card">
                        <div class="roi-value">{DOWNTIME_AVOIDED}</div>
                        <div class="roi-label">Downtime Avoided</div>
                    </div>
                    <div class="roi-card">
                        <div class="roi-value">{COMPLIANCE_VALUE}</div>
                        <div class="roi-label">Compliance Value</div>
                    </div>
                    <div class="roi-card">
                        <div class="roi-value">{TIME_SAVED}</div>
                        <div class="roi-label">Time Saved</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Critical Incidents & Recoveries -->
        <div class="executive-summary">
            <div class="section-header">
                <div class="section-icon">🚨</div>
                <div class="section-title">Critical Incidents & Recoveries</div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 25px;">
                {RECOVERY_EVENTS}
            </div>
        </div>

        <!-- Recommendations -->
        <div class="recommendations">
            <div class="section-header">
                <div class="section-icon">💡</div>
                <div class="section-title">Strategic Recommendations</div>
            </div>

            {RECOMMENDATIONS}
        </div>

        <!-- QBR Footer -->
        <div class="qbr-footer">
            <div class="footer-content">
                <h3 style="margin-bottom: 15px; color: white;">Partnership Summary</h3>
                <p style="font-size: 1.1rem; line-height: 1.6;">
                    This quarter demonstrates the strong value partnership between our MSP services and {CLIENT_NAME}'s business operations. With {ROI_PERCENTAGE}% ROI and {CRITICAL_RECOVERIES} critical recovery successes, our backup and recovery services continue to provide essential business protection and operational continuity.
                </p>
                <p style="margin-top: 15px; font-size: 0.9rem; opacity: 0.8;">
                    Next QBR scheduled for {NEXT_QBR_DATE} | 24/7 Support: {MSP_SUPPORT_EMAIL} | Emergency: {MSP_EMERGENCY_PHONE}
                </p>
            </div>
        </div>
    </div>

    <script>
        // Add loading animations and interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Animate value cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'slideInUp 0.6s ease forwards';
                    }
                });
            }, observerOptions);

            // Observe all cards
            document.querySelectorAll('.value-card, .service-card, .asset-item').forEach(card => {
                observer.observe(card);
            });

            // Add click interactions for detailed views
            document.querySelectorAll('.asset-item').forEach(asset => {
                asset.addEventListener('click', function() {
                    const assetName = this.querySelector('.asset-name').textContent;
                    alert(`${assetName} - Click for detailed backup history and metrics`);
                });
            });

            // Animate ROI numbers
            setTimeout(() => {
                const roiValues = document.querySelectorAll('.roi-value');
                roiValues.forEach(value => {
                    const text = value.textContent;
                    value.textContent = '0';
                    
                    let counter = 0;
                    const target = parseInt(text.replace(/[^\d]/g, '')) || 100;
                    const increment = target / 50;
                    
                    const timer = setInterval(() => {
                        counter += increment;
                        if (counter >= target) {
                            value.textContent = text;
                            clearInterval(timer);
                        } else {
                            const suffix = text.includes('%') ? '%' : text.includes('$') ? '$' : '';
                            const prefix = text.includes('$') ? '$' : '';
                            value.textContent = prefix + Math.floor(counter) + (suffix === '$' ? '' : suffix);
                        }
                    }, 30);
                });
            }, 2000);
        });

        // Add CSS animation keyframes
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>