#!/usr/bin/env python3
"""
QBR HTML Template Manager for Datto Partner Portal MCP Server
Handles HTML template loading and processing for quarterly business review reports.
"""

import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional

def format_storage_size(gb_value: float) -> str:
    """Format storage size in appropriate units"""
    if gb_value >= 1024:
        return f"{gb_value / 1024:.1f}TB"
    else:
        return f"{gb_value:.0f}GB"

def format_currency(amount: float) -> str:
    """Format currency values"""
    return f"${amount:,.2f}"

def format_timestamp(timestamp: Any) -> str:
    """Format timestamp for display"""
    if timestamp == 0 or timestamp is None:
        return "No recent backup"
    
    try:
        if isinstance(timestamp, str):
            # Try to parse string timestamp
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        elif isinstance(timestamp, (int, float)):
            # Unix timestamp
            dt = datetime.fromtimestamp(timestamp)
        else:
            return "Invalid date"
        
        return dt.strftime("%m/%d %H:%M")
    except (ValueError, TypeError, OSError):
        return "Invalid date"

# Load QBR HTML template
def load_qbr_template() -> str:
    """Load and prepare the QBR HTML template"""
    template_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{CLIENT_NAME} - {QUARTER} QBR</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        /* Header Section */
        .header {{
            text-align: center;
            padding: 60px 20px;
            background: radial-gradient(ellipse at center, rgba(0, 136, 255, 0.1) 0%, transparent 70%);
            position: relative;
            overflow: hidden;
        }}
        
        .header::before {{
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(0, 136, 255, 0.1), transparent);
            animation: shine 3s infinite;
        }}
        
        @keyframes shine {{
            0% {{ transform: translateX(-100%) translateY(-100%) rotate(45deg); }}
            100% {{ transform: translateX(100%) translateY(100%) rotate(45deg); }}
        }}
        
        .header h1 {{
            font-size: 4rem;
            font-weight: 300;
            letter-spacing: -2px;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #00d4ff 0%, #0088ff 50%, #00d4ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradient 3s ease infinite;
            background-size: 200% 200%;
        }}
        
        @keyframes gradient {{
            0% {{ background-position: 0% 50%; }}
            50% {{ background-position: 100% 50%; }}
            100% {{ background-position: 0% 50%; }}
        }}
        
        .header .subtitle {{
            font-size: 1.5rem;
            color: #888;
            margin-bottom: 40px;
        }}
        
        .client-info {{
            display: flex;
            justify-content: center;
            gap: 40px;
            flex-wrap: wrap;
        }}
        
        .client-info div {{
            text-align: center;
        }}
        
        .client-info .label {{
            font-size: 0.875rem;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        
        .client-info .value {{
            font-size: 1.25rem;
            color: #fff;
            font-weight: 600;
        }}
        
        /* Stats Cards */
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }}
        
        .stat-card {{
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }}
        
        .stat-card:hover {{
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(0, 136, 255, 0.5);
            box-shadow: 0 10px 40px rgba(0, 136, 255, 0.2);
        }}
        
        .stat-card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00d4ff, #0088ff);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }}
        
        .stat-card:hover::before {{
            transform: scaleX(1);
        }}
        
        .stat-card .icon {{
            width: 60px;
            height: 60px;
            margin-bottom: 20px;
            background: linear-gradient(135deg, rgba(0, 136, 255, 0.2), rgba(0, 212, 255, 0.2));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
        }}
        
        .stat-card h3 {{
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #fff 0%, #ccc 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }}
        
        .stat-card .label {{
            font-size: 1rem;
            color: #888;
            margin-bottom: 5px;
        }}
        
        .stat-card .sublabel {{
            font-size: 0.875rem;
            color: #666;
        }}
        
        /* Value Delivered Section */
        .value-section {{
            background: rgba(255, 255, 255, 0.02);
            border-radius: 24px;
            padding: 60px;
            margin: 60px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }}
        
        .value-section::before {{
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(0, 136, 255, 0.1) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }}
        
        @keyframes pulse {{
            0%, 100% {{ transform: scale(1); opacity: 0.5; }}
            50% {{ transform: scale(1.2); opacity: 0.8; }}
        }}
        
        .value-section h2 {{
            font-size: 2.5rem;
            margin-bottom: 40px;
            position: relative;
        }}
        
        .value-amount {{
            font-size: 5rem;
            font-weight: 700;
            color: #00d4ff;
            text-shadow: 0 0 40px rgba(0, 212, 255, 0.5);
            margin-bottom: 20px;
            position: relative;
        }}
        
        .value-description {{
            font-size: 1.25rem;
            color: #888;
            max-width: 600px;
            margin: 0 auto;
            position: relative;
        }}
        
        /* Service Overview */
        .services-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 60px 0;
        }}
        
        .service-card {{
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }}
        
        .service-card::after {{
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(135deg, #00d4ff, #0088ff, #00d4ff);
            border-radius: 20px;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
        }}
        
        .service-card:hover::after {{
            opacity: 1;
        }}
        
        .service-card:hover {{
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.08);
        }}
        
        .service-card h3 {{
            font-size: 1.75rem;
            margin-bottom: 20px;
            color: #00d4ff;
        }}
        
        .service-card .metric {{
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
        }}
        
        .service-card .metric-label {{
            font-size: 1rem;
            color: #888;
        }}
        
        /* Executive Summary */
        .executive-summary {{
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 25px 50px rgba(0, 136, 255, 0.1);
        }}
        
        .section-header {{
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }}
        
        .section-icon {{
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, rgba(0, 136, 255, 0.2), rgba(0, 212, 255, 0.2));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 1.5rem;
            color: #00d4ff;
        }}
        
        .section-title {{
            font-size: 2.2rem;
            font-weight: 700;
            color: #fff;
        }}
        
        .value-metrics {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }}
        
        .value-card {{
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }}
        
        .value-card:hover {{
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(0, 136, 255, 0.5);
            box-shadow: 0 10px 40px rgba(0, 136, 255, 0.2);
        }}
        
        .value-card::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00d4ff, #0088ff);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }}
        
        .value-card:hover::before {{
            transform: scaleX(1);
        }}
        
        .value-number {{
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #fff 0%, #ccc 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }}
        
        .value-label {{
            font-size: 1rem;
            color: #888;
            margin-bottom: 5px;
        }}
        
        .value-description {{
            font-size: 0.875rem;
            color: #666;
        }}
        
        /* Asset Details */
        .asset-details {{
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 35px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 136, 255, 0.1);
        }}
        
        .asset-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }}
        
        .asset-item {{
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            border-left: 4px solid #00d4ff;
        }}
        
        .asset-name {{
            font-size: 1.3rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 10px;
        }}
        
        .asset-type {{
            font-size: 0.9rem;
            color: #00d4ff;
            font-weight: 600;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        
        .asset-metrics {{
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }}
        
        .metric-item {{
            text-align: center;
        }}
        
        .metric-value {{
            font-size: 1.4rem;
            font-weight: 700;
            color: #00d4ff;
        }}
        
        .metric-label {{
            font-size: 0.8rem;
            color: #888;
            margin-top: 5px;
        }}
        
        /* Screenshot Gallery */
        .screenshot-section {{
            margin: 80px 0;
        }}
        
        .screenshot-section h2 {{
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 50px;
        }}
        
        .screenshot-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
        }}
        
        .screenshot-card {{
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
        }}
        
        .screenshot-card:hover {{
            transform: scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 136, 255, 0.3);
        }}
        
        .screenshot-card img {{
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
        }}
        
        .screenshot-card .info {{
            padding: 20px;
        }}
        
        .screenshot-card .asset-name {{
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 10px;
        }}
        
        .screenshot-card .asset-details {{
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255,255,255,0.1);
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            font-size: 0.85rem;
        }}
        
        .screenshot-card .asset-details .detail-item {{
            color: #ccc;
        }}
        
        .screenshot-card .asset-details .detail-item span {{
            font-weight: 600;
            color: #fff;
        }}
        
        .screenshot-card .screenshot-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }}
        
        .screenshot-card .screenshot-meta {{
            font-size: 0.875rem;
            color: #888;
            margin-bottom: 10px;
        }}
        
        .status-badge {{
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}
        
        .status-success {{
            background: rgba(0, 255, 136, 0.2);
            color: #00ff88;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
        }}
        
        .status-failed {{
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 600;
        }}
        
        /* Recommendations */
        .recommendations {{
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 35px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 136, 255, 0.1);
        }}
        
        .rec-item {{
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.2);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #ffc107;
        }}
        
        .rec-priority {{
            font-size: 0.85rem;
            font-weight: 700;
            color: #ffc107;
            text-transform: uppercase;
            margin-bottom: 10px;
        }}
        
        .rec-title {{
            font-size: 1.3rem;
            font-weight: 700;
            color: #fff;
            margin-bottom: 10px;
        }}
        
        .rec-description {{
            color: #ccc;
            line-height: 1.6;
            margin-bottom: 15px;
        }}
        
        .rec-value {{
            background: rgba(0, 255, 136, 0.1);
            color: #00ff88;
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
        }}
        
        /* Summary Section */
        .summary-section {{
            background: linear-gradient(135deg, rgba(0, 136, 255, 0.1) 0%, rgba(0, 212, 255, 0.05) 100%);
            border-radius: 24px;
            padding: 80px 40px;
            margin: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }}
        
        .summary-section::before {{
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.05), transparent);
            animation: sweep 4s linear infinite;
        }}
        
        @keyframes sweep {{
            0% {{ transform: rotate(0deg); }}
            100% {{ transform: rotate(360deg); }}
        }}
        
        .summary-section h2 {{
            font-size: 3rem;
            margin-bottom: 30px;
            position: relative;
        }}
        
        .summary-section p {{
            font-size: 1.25rem;
            color: #bbb;
            max-width: 800px;
            margin: 0 auto 40px;
            position: relative;
        }}
        
        .slogan {{
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #00d4ff 0%, #0088ff 50%, #00ff88 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            animation: gradient 3s ease infinite;
            background-size: 200% 200%;
        }}
        
        /* Responsive */
        @media (max-width: 768px) {{
            .header h1 {{ font-size: 2.5rem; }}
            .value-amount {{ font-size: 3rem; }}
            .stat-card h3 {{ font-size: 2rem; }}
            .service-card .metric {{ font-size: 2rem; }}
            .slogan {{ font-size: 1.75rem; }}
            .container {{ padding: 15px; }}
            .services-grid {{ grid-template-columns: 1fr; }}
            .value-metrics {{ grid-template-columns: 1fr; }}
            .stats-grid {{ grid-template-columns: 1fr; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>Quarterly Business Review</h1>
            <p class="subtitle">{QUARTER} Performance Report</p>
            <div class="client-info">
                <div>
                    <p class="label">Client</p>
                    <p class="value">{CLIENT_NAME}</p>
                </div>
                <div>
                    <p class="label">MSP Partner</p>
                    <p class="value">{MSP_INITIALS}</p>
                </div>
                <div>
                    <p class="label">Review Period</p>
                    <p class="value">{QUARTER}</p>
                </div>
            </div>
        </header>

        <!-- Key Stats -->
        <section class="stats-grid">
            <div class="stat-card">
                <div class="icon">🛡️</div>
                <h3>{UPTIME_PERCENTAGE}%</h3>
                <p class="label">Backup Success Rate</p>
                <p class="sublabel">Zero data loss incidents</p>
            </div>
            <div class="stat-card">
                <div class="icon">⚡</div>
                <h3>15min</h3>
                <p class="label">Average RTO</p>
                <p class="sublabel">Rapid recovery capability</p>
            </div>
            <div class="stat-card">
                <div class="icon">💾</div>
                <h3>{BCDR_DATA_VOLUME}</h3>
                <p class="label">Protected Data</p>
                <p class="sublabel">Across all platforms</p>
            </div>
            <div class="stat-card">
                <div class="icon">🔒</div>
                <h3>{CRITICAL_RECOVERIES}</h3>
                <p class="label">Ransomware Attacks Prevented</p>
                <p class="sublabel">{QUARTER}</p>
            </div>
        </section>

        <!-- Value Delivered -->
        <section class="value-section">
            <h2>Service Excellence in {QUARTER}</h2>
            <div class="value-amount">{TOTAL_VALUE_DELIVERED}</div>
            <p class="value-description">
                Maintained 100% business continuity with zero data loss incidents and comprehensive protection
            </p>
        </section>

        <!-- Service Overview -->
        <section>
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 50px; color: #fff;">Protection Services Overview</h2>
            <div class="services-grid">
                <div class="service-card">
                    <h3>BCDR Protection</h3>
                    <div class="metric">{BCDR_PROTECTED_ASSETS}</div>
                    <p class="metric-label">Protected Assets</p>
                    <p style="margin-top: 20px; color: #888;">
                        {BCDR_PROTECTED_ASSETS} Active Siris devices providing comprehensive backup and instant recovery
                    </p>
                </div>
                <div class="service-card">
                    <h3>SaaS Protection</h3>
                    <div class="metric">{SAAS_USER_SEATS}</div>
                    <p class="metric-label">Protected Users</p>
                    <p style="margin-top: 20px; color: #888;">
                        Complete Office 365 backup with mailbox, SharePoint, and Teams protection
                    </p>
                </div>
                <div class="service-card">
                    <h3>Data Protection</h3>
                    <div class="metric">{BCDR_DATA_VOLUME}</div>
                    <p class="metric-label">Total Protected Data</p>
                    <p style="margin-top: 20px; color: #888;">
                        Comprehensive protection across all business-critical systems and data
                    </p>
                </div>
            </div>
        </section>

        <!-- Executive Summary -->
        <div class="executive-summary">
            <div class="section-header">
                <div class="section-icon">📊</div>
                <div class="section-title">Executive Summary</div>
            </div>
            
            <div class="value-metrics">
                <div class="value-card">
                    <div class="value-number">{UPTIME_PERCENTAGE}%</div>
                    <div class="value-label">Quarterly Uptime</div>
                    <div class="value-description">Exceeded 99% SLA target with minimal business disruption</div>
                </div>
                <div class="value-card">
                    <div class="value-number">{PROTECTED_USERS}</div>
                    <div class="value-label">Protected Users</div>
                    <div class="value-description">Complete SaaS backup coverage for all Office 365 accounts</div>
                </div>
                <div class="value-card">
                    <div class="value-number">{SAAS_SUCCESSFUL_BACKUPS}</div>
                    <div class="value-label">Successful Backups</div>
                    <div class="value-description">Reliable data protection across all services</div>
                </div>
                <div class="value-card">
                    <div class="value-number">{CRITICAL_RECOVERIES}</div>
                    <div class="value-label">Critical Recoveries</div>
                    <div class="value-description">Successful data restoration preventing business loss</div>
                </div>
            </div>

            <p style="font-size: 1.1rem; line-height: 1.8; color: #ccc; background: rgba(255, 255, 255, 0.05); padding: 25px; border-radius: 15px; border-left: 4px solid #00d4ff;">
                <strong>Quarter Highlights:</strong> {CLIENT_NAME}'s backup infrastructure delivered exceptional performance this quarter with {UPTIME_PERCENTAGE}% uptime, protecting {PROTECTED_USERS} users. The implementation of enhanced monitoring and proactive maintenance has ensured optimal system reliability and zero data loss incidents.
            </p>
        </div>
        
        <!-- Asset Details -->
        <div class="executive-summary">
            <div class="section-header">
                <div class="section-icon">💾</div>
                <div class="section-title">Protected Asset Portfolio</div>
            </div>
            
            <div class="asset-grid">
                {ASSET_PORTFOLIO}
            </div>
        </div>

        <!-- Screenshot Gallery -->
        <section class="screenshot-section">
            <h2 style="font-size: 2.5rem; text-align: center; margin-bottom: 50px; color: #fff;">Latest System Verification Screenshots</h2>
            <div class="screenshot-grid">
                {SCREENSHOT_GALLERY}
            </div>
        </section>

        <!-- BCDR Appliance Health & Capacity Section -->
        {BCDR_DEVICES_SECTION}

        {ROI_SECTION}

        <!-- Critical Incidents & Recoveries -->
        <div class="executive-summary">
            <div class="section-header">
                <div class="section-icon">🚨</div>
                <div class="section-title">Critical Incidents & Recoveries</div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 25px;">
                {RECOVERY_EVENTS}
            </div>
        </div>

        <!-- Recommendations -->
        <div class="recommendations">
            <div class="section-header">
                <div class="section-icon">💡</div>
                <div class="section-title">Strategic Recommendations</div>
            </div>

            {RECOMMENDATIONS}
        </div>

        <!-- Summary -->
        <section class="summary-section">
            <h2>{QUARTER} Summary</h2>
            <p>
                Throughout {QUARTER}, {MSP_INITIALS} has maintained exceptional service levels for {CLIENT_NAME}, 
                achieving {UPTIME_PERCENTAGE}% backup success rates and preventing {CRITICAL_RECOVERIES} potential incidents. Our 
                comprehensive protection strategy across BCDR, SaaS, and data platforms has ensured zero downtime 
                and complete data integrity for all critical systems.
            </p>
            <p>
                {SUMMARY_VALUE_TEXT}
            </p>
            <div class="slogan">
                "Your Success is Our Mission – Always Protected, Always Ready"
            </div>
        </section>
    </div>

    <script>
        // Add loading animations and interactions
        document.addEventListener('DOMContentLoaded', function() {{
            // Animate value cards on scroll
            const observerOptions = {{
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            }};

            const observer = new IntersectionObserver((entries) => {{
                entries.forEach(entry => {{
                    if (entry.isIntersecting) {{
                        entry.target.style.animation = 'slideInUp 0.6s ease forwards';
                    }}
                }});
            }}, observerOptions);

            // Observe all cards
            document.querySelectorAll('.value-card, .service-card, .asset-item').forEach(card => {{
                observer.observe(card);
            }});

            // Add click interactions for detailed views
            document.querySelectorAll('.asset-item').forEach(asset => {{
                asset.addEventListener('click', function() {{
                    const assetName = this.querySelector('.asset-name').textContent;
                    alert(`${{assetName}} - Click for detailed backup history and metrics`);
                }});
            }});

            // Animate ROI numbers
            setTimeout(() => {{
                const roiValues = document.querySelectorAll('.roi-value');
                roiValues.forEach(value => {{
                    const text = value.textContent;
                    value.textContent = '0';
                    
                    let counter = 0;
                    const target = parseInt(text.replace(/[^\\d]/g, '')) || 100;
                    const increment = target / 50;
                    
                    const timer = setInterval(() => {{
                        counter += increment;
                        if (counter >= target) {{
                            value.textContent = text;
                            clearInterval(timer);
                        }} else {{
                            const suffix = text.includes('%') ? '%' : text.includes('$') ? '$' : '';
                            const prefix = text.includes('$') ? '$' : '';
                            value.textContent = prefix + Math.floor(counter) + (suffix === '$' ? '' : suffix);
                        }}
                    }}, 30);
                }});
            }}, 2000);
        }});

        // Add CSS animation keyframes
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInUp {{
                from {{
                    opacity: 0;
                    transform: translateY(30px);
                }}
                to {{
                    opacity: 1;
                    transform: translateY(0);
                }}
            }}
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>'''
    
    return template_content

# Load the template at module level
QBR_TEMPLATE = load_qbr_template()