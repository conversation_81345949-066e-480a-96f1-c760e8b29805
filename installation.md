# Installation Guide

This guide covers the installation and initial setup of the Datto Partner Portal MCP Server.

## Prerequisites

- Python 3.9 or higher
- Valid <PERSON>tto Partner Portal API credentials (public key and secret key)
- Network access to `api.datto.com`
- 500MB free disk space (for artifacts and logs)

## Quick Installation

### Windows

1. Download or clone the project to your desired directory
2. Open Command Prompt as Administrator
3. Navigate to the project directory
4. Run the setup script:
   ```batch
   setup.bat
   ```
5. Follow the prompts to complete installation

### Mac/Linux

1. Download or clone the project to your desired directory
2. Open Terminal
3. Navigate to the project directory
4. Make the setup script executable and run it:
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```
5. Follow the prompts to complete installation

## Manual Installation

If you prefer to install manually or the automated scripts don't work in your environment:

### 1. Create Virtual Environment

```bash
# Mac/Linux
python3 -m venv venv
source venv/bin/activate

# Windows
python -m venv venv
venv\Scripts\activate
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Create Configuration

```bash
# Copy the example configuration
cp config/config-example.yaml config/dpp-config.yaml

# Edit with your API credentials
nano config/dpp-config.yaml  # or your preferred editor
```

### 4. Create Required Directories

```bash
mkdir -p logs artifacts
```

## Configuration

### Required Settings

Edit `config/dpp-config.yaml` and set the following required values:

```yaml
api:
  public_key: "YOUR_DATTO_PUBLIC_KEY"
  secret_key: "YOUR_DATTO_SECRET_KEY"
```

### Environment Variables (Alternative)

Instead of editing the YAML file directly, you can set environment variables:

```bash
# Mac/Linux
export DATTO_PUBLIC_KEY="your_public_key_here"
export DATTO_SECRET_KEY="your_secret_key_here"

# Windows
set DATTO_PUBLIC_KEY=your_public_key_here
set DATTO_SECRET_KEY=your_secret_key_here
```

## Obtaining Datto API Credentials

1. Log in to your Datto Partner Portal
2. Navigate to **API Development** section
3. Generate or retrieve your API credentials:
   - Public Key (username)
   - Secret Key (password)
4. Ensure the API user has appropriate permissions for:
   - BCDR device access
   - DTC asset access
   - SaaS Protection access

## Starting the Server

### Using Generated Scripts

```bash
# Windows
start-dpp-mcp.bat

# Mac/Linux
./start-dpp-mcp.sh
```

### Manual Start

```bash
# Activate virtual environment first
source venv/bin/activate  # Mac/Linux
# or
venv\Scripts\activate  # Windows

# Start the server
python src/dpp_mcp_server.py
```

## Verification

### Test API Connection

The server will test your API connection on startup. Look for these log messages:

```
INFO - Starting Datto Partner Portal MCP Server
INFO - Configuration loaded from: /path/to/config/dpp-config.yaml
INFO - API client initialized successfully
```

### Test Basic Functionality

Try running a basic health check:
```bash
# In another terminal/command prompt
curl http://localhost:8000/health
```

Or use the analyze_backup_health tool through your MCP client.

## Troubleshooting Installation

### Python Version Issues

- Ensure Python 3.9+ is installed: `python --version`
- On some systems, use `python3` instead of `python`
- Windows: Install from [python.org](https://python.org)
- Mac: `brew install python3`
- Ubuntu/Debian: `sudo apt install python3 python3-pip python3-venv`

### Permission Issues

- Windows: Run Command Prompt as Administrator
- Mac/Linux: Ensure you have write permissions to the installation directory
- Use `sudo` only if necessary and understand the security implications

### Network Issues

- Verify internet connectivity
- Check firewall settings for outbound HTTPS (port 443)
- Corporate networks may require proxy configuration
- Test API access: `curl https://api.datto.com/v1/`

### Dependency Issues

If package installation fails:

```bash
# Upgrade pip first
python -m pip install --upgrade pip

# Try installing with verbose output
pip install -r requirements.txt -v

# If behind corporate firewall
pip install -r requirements.txt --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org
```

### Configuration Issues

- Verify YAML syntax: `python -c "import yaml; yaml.safe_load(open('config/dpp-config.yaml'))"`
- Check file permissions: configuration file should be readable by the user running the server
- Validate API credentials with a simple test

## Performance Considerations

### Resource Requirements

- **RAM**: 512MB minimum, 1GB recommended
- **CPU**: 1 core minimum, 2+ cores recommended for heavy usage
- **Disk**: 100MB for application, additional space for artifacts
- **Network**: Stable internet connection, ~1MB/hour typical usage

### Tuning for Large Environments

For environments with 100+ devices or frequent reporting:

```yaml
performance:
  max_devices_per_query: 25  # Reduce from default 50
  max_assets_per_device: 5   # Reduce from default 10
  screenshot_limit: 4        # Reduce from default 8
```

### Production Deployment

For production deployments:

1. **Use a dedicated service account** for the MCP server
2. **Set up log rotation** to manage disk space
3. **Monitor resource usage** and adjust limits accordingly
4. **Configure automatic startup** using your system's service manager
5. **Set up monitoring** for the MCP server process
6. **Regular backup** of configuration and artifacts

## Next Steps

After successful installation:

1. Read the [Configuration Guide](configuration.md) for detailed customization options
2. Review the [API Reference](api-reference.md) for available tools and resources
3. Test QBR generation with sample data
4. Configure monitoring and alerting for production use

## Getting Help

If you encounter issues during installation:

1. Check the [Troubleshooting Guide](troubleshooting.md)
2. Review the server logs in the `logs/` directory
3. Verify your Datto API credentials are correct and have appropriate permissions
4. Contact your Datto partner representative for API access issues