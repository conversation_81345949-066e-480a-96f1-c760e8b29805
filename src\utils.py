#!/usr/bin/env python3
"""
Utility functions for Datto Partner MCP Server
Logging, validation, and helper functions.
"""

import os
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional

from config import LoggingConfig

def setup_logging(logging_config: LoggingConfig) -> None:
    """Setup logging with rotation and partner-specific configuration."""
    
    # Create logs directory if it doesn't exist
    log_file_path = Path(logging_config.file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, logging_config.level.upper(), logging.INFO))
    
    # Remove existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=logging_config.file,
        maxBytes=logging_config.max_size_mb * 1024 * 1024,
        backupCount=logging_config.backup_count
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    
    # Formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # Log startup message
    logger.info("Datto Partner MCP Server logging initialized")

def validate_api_credentials(public_key: str, secret_key: str) -> bool:
    """Validate that API credentials are provided and not placeholder values."""
    if not public_key or not secret_key:
        return False
    
    # Check for common placeholder values
    placeholders = [
        "your_public_key", "your_secret_key", 
        "DATTO_PUBLIC_KEY", "DATTO_SECRET_KEY",
        "${DATTO_PUBLIC_KEY}", "${DATTO_SECRET_KEY}",
        "placeholder", "change_me", "update_me"
    ]
    
    if public_key.lower() in [p.lower() for p in placeholders]:
        return False
    if secret_key.lower() in [p.lower() for p in placeholders]:
        return False
    
    # Basic format validation (Datto keys are typically UUIDs)
    if len(public_key) < 10 or len(secret_key) < 10:
        return False
    
    return True

def format_currency(amount: float, include_cents: bool = False) -> str:
    """Format currency amounts consistently."""
    if include_cents:
        return f"${amount:,.2f}"
    else:
        return f"${amount:,.0f}"

def format_percentage(value: float, decimal_places: int = 1) -> str:
    """Format percentage values consistently."""
    return f"{value:.{decimal_places}f}%"

def format_file_size(bytes_count: float) -> str:
    """Format file sizes in human-readable format."""
    if bytes_count < 1024:
        return f"{bytes_count:.0f} B"
    elif bytes_count < 1024 * 1024:
        return f"{bytes_count / 1024:.1f} KB"
    elif bytes_count < 1024 * 1024 * 1024:
        return f"{bytes_count / (1024 * 1024):.1f} MB"
    elif bytes_count < 1024 * 1024 * 1024 * 1024:
        return f"{bytes_count / (1024 * 1024 * 1024):.1f} GB"
    else:
        return f"{bytes_count / (1024 * 1024 * 1024 * 1024):.1f} TB"

def calculate_business_size_category(employee_count: int) -> str:
    """Determine business size category for cost calculations."""
    if employee_count <= 100:
        return "small_business"
    elif employee_count <= 1000:
        return "mid_market"
    else:
        return "enterprise"

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """Safely divide two numbers, returning default if denominator is zero."""
    if denominator == 0:
        return default
    return numerator / denominator

def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file operations."""
    import re
    # Remove or replace invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    # Limit length
    if len(filename) > 200:
        filename = filename[:200]
    return filename

def generate_correlation_id() -> str:
    """Generate unique correlation ID for alert grouping."""
    import uuid
    return str(uuid.uuid4())[:8]

def parse_api_timestamp(timestamp_str: str) -> Optional[Any]:
    """Parse API timestamp strings safely."""
    if not timestamp_str:
        return None
    
    try:
        from datetime import datetime
        # Handle various timestamp formats
        if timestamp_str.endswith('Z'):
            return datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        else:
            return datetime.fromisoformat(timestamp_str)
    except (ValueError, AttributeError):
        return None

def mask_sensitive_data(data: str, mask_char: str = '*', visible_chars: int = 4) -> str:
    """Mask sensitive data for logging."""
    if len(data) <= visible_chars:
        return mask_char * len(data)
    
    return data[:visible_chars] + mask_char * (len(data) - visible_chars)

def validate_email(email: str) -> bool:
    """Basic email validation."""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_phone(phone: str) -> bool:
    """Basic phone number validation."""
    import re
    # Remove common formatting characters
    cleaned = re.sub(r'[^\d]', '', phone)
    # Check if it's a reasonable length (7-15 digits)
    return 7 <= len(cleaned) <= 15

def get_quarter_from_date(date_obj: Any = None) -> str:
    """Get quarter string from date object."""
    from datetime import datetime
    if date_obj is None:
        date_obj = datetime.now()
    
    quarter = ((date_obj.month - 1) // 3) + 1
    return f"Q{quarter} {date_obj.year}"

def calculate_next_quarter_date(current_date: Any = None) -> str:
    """Calculate next quarter date for QBR scheduling."""
    from datetime import datetime
    if current_date is None:
        current_date = datetime.now()
    
    current_quarter = ((current_date.month - 1) // 3) + 1
    next_quarter = current_quarter + 1 if current_quarter < 4 else 1
    next_year = current_date.year if next_quarter > current_quarter else current_date.year + 1
    
    quarter_months = {1: "February", 2: "May", 3: "August", 4: "November"}
    return f"{quarter_months[next_quarter]} {next_year}"

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length with suffix."""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def retry_on_exception(max_retries: int = 3, delay: float = 1.0):
    """Decorator for retrying functions on exception."""
    import time
    import functools
    
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        await asyncio.sleep(delay * (2 ** attempt))  # Exponential backoff
                    else:
                        raise last_exception
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        time.sleep(delay * (2 ** attempt))  # Exponential backoff
                    else:
                        raise last_exception
        
        return async_wrapper if hasattr(func, '__code__') and 'async' in str(func.__code__.co_flags) else sync_wrapper
    
    return decorator

def create_health_check_summary(health_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create standardized health check summary."""
    return {
        "timestamp": datetime.now().isoformat(),
        "overall_health": health_data.get("health_score", 0),
        "status": health_data.get("health_status", "Unknown"),
        "devices_monitored": health_data.get("total_devices", 0),
        "active_issues": health_data.get("devices_with_alerts", 0),
        "protected_assets": health_data.get("total_protected_assets", 0),
        "recommendations_count": len(health_data.get("recommendations", [])),
        "partner_monitoring": True
    }

def validate_configuration_completeness(config) -> Dict[str, Any]:
    """Validate that configuration is complete and usable."""
    issues = []
    warnings = []
    
    # Check required partner info
    if not config.partner.name or config.partner.name == "Your MSP Name":
        issues.append("Partner name not configured")
    
    if not config.partner.support_email or not validate_email(config.partner.support_email):
        issues.append("Valid partner support email not configured")
    
    if not config.partner.emergency_phone:
        warnings.append("Emergency phone number not configured")
    
    # Check API credentials
    if not validate_api_credentials(config.datto.public_key, config.datto.secret_key):
        issues.append("Valid Datto API credentials not configured")
    
    # Check feature configuration
    enabled_features = [k for k, v in vars(config.features).items() if v]
    if len(enabled_features) == 0:
        warnings.append("No features enabled - limited functionality")
    
    return {
        "valid": len(issues) == 0,
        "issues": issues,
        "warnings": warnings,
        "enabled_features": enabled_features,
        "completeness_score": max(0, 100 - (len(issues) * 25) - (len(warnings) * 5))
    }