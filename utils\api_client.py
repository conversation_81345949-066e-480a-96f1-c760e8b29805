"""
Datto Partner Portal API Client
Handles authentication, rate limiting, and error handling for Datto API calls.
"""

from typing import Dict, Any, Optional
import httpx
from pydantic import BaseModel, Field


class DattoConfig(BaseModel):
    """Configuration for Datto API access."""
    public_key: str = Field(..., description="Datto API public key")
    secret_key: str = Field(..., description="Datto API secret key") 
    base_url: str = Field(default="https://api.datto.com", description="Datto API base URL")
    timeout_seconds: int = Field(default=60, description="Request timeout in seconds")
    rate_limit_budget: int = Field(default=10000, description="Rate limit budget per hour")


class DattoClient:
    """HTTP client for Datto Partner Portal API with rate limiting and error handling."""
    
    def __init__(self, config: DattoConfig):
        self.config = config
        self.client = httpx.AsyncClient(
            timeout=config.timeout_seconds,
            base_url=config.base_url,
            auth=(config.public_key, config.secret_key)
        )
        self.rate_limit_remaining = config.rate_limit_budget
    
    async def request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make authenticated request with automatic error handling."""
        response = await self.client.request(method, endpoint, **kwargs)
        
        # Update rate limit tracking
        if 'X-API-Limit-Remaining' in response.headers:
            self.rate_limit_remaining = int(response.headers['X-API-Limit-Remaining'])
        
        response.raise_for_status()
        
        # Handle empty responses
        if not response.content:
            return {"items": [], "message": "Empty response"}
        
        data = response.json()
        
        # Normalize list responses to consistent format
        if isinstance(data, list):
            return {"items": data}
        return data
    
    async def get(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """GET request helper."""
        return await self.request("GET", endpoint, **kwargs)
    
    async def put(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """PUT request helper."""
        return await self.request("PUT", endpoint, **kwargs)
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
    
    @property
    def rate_limit_status(self) -> Dict[str, Any]:
        """Get current rate limit status."""
        return {
            "remaining": self.rate_limit_remaining,
            "budget": self.config.rate_limit_budget,
            "usage_percent": ((self.config.rate_limit_budget - self.rate_limit_remaining) / 
                            self.config.rate_limit_budget * 100)
        }