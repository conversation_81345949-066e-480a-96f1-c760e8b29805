#!/usr/bin/env python3
"""
Configuration management for Datto Partner MCP Server
Handles loading and validation of partner configuration files.
"""

import os
import yaml
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, validator
from pathlib import Path

class PartnerInfo(BaseModel):
    """Partner/MSP information configuration."""
    name: str = Field(..., description="Full company name")
    initials: str = Field(..., description="2-3 letter abbreviation")
    support_email: str = Field(..., description="Primary support contact")
    emergency_phone: str = Field(..., description="24/7 emergency number")
    website: str = Field(default="", description="Company website")
    logo_url: str = Field(default="", description="Optional company logo URL")

class DattoConfig(BaseModel):
    """Datto API configuration."""
    public_key: str = Field(..., description="Datto API public key")
    secret_key: str = Field(..., description="Datto API secret key")
    base_url: str = Field(default="https://api.datto.com", description="API endpoint")
    rate_limit_budget: int = Field(default=10000, description="API calls per hour")

class CostCalculations(BaseModel):
    """Business impact cost calculation settings."""
    small_business_downtime_cost: int = Field(default=8600, description="$/hour for 1-100 employees")
    mid_market_downtime_cost: int = Field(default=35000, description="$/hour for 100-1000 employees")  
    enterprise_downtime_cost: int = Field(default=100000, description="$/hour for 1000+ employees")
    compliance_value_per_user: int = Field(default=150, description="$/user/year compliance value")
    audit_cost_avoided: int = Field(default=25000, description="Annual audit cost mitigation")
    fine_risk_mitigation: int = Field(default=50000, description="Potential fine mitigation")

class QBRConfig(BaseModel):
    """QBR generation configuration."""
    default_quarter: str = Field(default="auto", description="Default quarter or 'auto'")
    include_screenshots: bool = Field(default=True, description="Include screenshot section")
    cost_calculations: CostCalculations = Field(default_factory=CostCalculations)

class SaaSPricing(BaseModel):
    """SaaS protection pricing for license optimization."""
    office365: Dict[str, float] = Field(default={
        "user": 4.0,
        "shared_mailbox": 2.0,
        "site": 1.0,
        "team": 1.0
    })
    google_workspace: Dict[str, float] = Field(default={
        "user": 3.0,
        "shared_drive": 1.0,
        "team": 1.0
    })

class CompanyColors(BaseModel):
    """Company branding colors."""
    primary: str = Field(default="#667eea", description="Primary brand color")
    secondary: str = Field(default="#764ba2", description="Secondary brand color")
    accent: str = Field(default="#60a5fa", description="Accent color")

class DashboardConfig(BaseModel):
    """Dashboard appearance configuration."""
    theme: str = Field(default="dark", description="UI theme")
    company_colors: CompanyColors = Field(default_factory=CompanyColors)
    refresh_interval: int = Field(default=300, description="Auto-refresh seconds")

class AlertConfig(BaseModel):
    """Alert correlation settings."""
    correlation_window_hours: int = Field(default=4, description="Alert grouping window")
    noise_reduction_threshold: int = Field(default=2, description="Min alerts for correlation")
    critical_device_threshold: int = Field(default=3, description="Devices for critical severity")

class RiskConfig(BaseModel):
    """Risk assessment configuration."""
    high_risk_threshold: int = Field(default=70, description="High risk threshold")
    medium_risk_threshold: int = Field(default=40, description="Medium risk threshold")
    backup_age_warning_hours: int = Field(default=24, description="Backup age warning")
    backup_age_critical_hours: int = Field(default=48, description="Backup age critical")
    screenshot_failure_risk_points: int = Field(default=25, description="Screenshot failure risk")

class RecommendationsConfig(BaseModel):
    """MSP recommendation settings."""
    server_refresh_cost_per_server: int = Field(default=15000, description="Annual cost per old server")
    storage_expansion_performance_gain: int = Field(default=25, description="% performance gain")
    security_stack_annual_savings: int = Field(default=12000, description="Annual security savings")
    compliance_automation_hours_saved: int = Field(default=40, description="Hours saved quarterly")

class FeatureFlags(BaseModel):
    """Feature enablement flags."""
    intelligent_alert_correlation: bool = Field(default=True)
    predictive_risk_assessment: bool = Field(default=True)
    saas_license_optimization: bool = Field(default=True)
    screenshot_verification: bool = Field(default=True)
    interactive_qbr: bool = Field(default=True)
    business_impact_calculations: bool = Field(default=True)

class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = Field(default="INFO", description="Log level")
    file: str = Field(default="logs/datto_mcp.log", description="Log file path")
    max_size_mb: int = Field(default=10, description="Max log file size")
    backup_count: int = Field(default=5, description="Log backup files")

class PerformanceConfig(BaseModel):
    """Performance and resource settings."""
    api_timeout_seconds: int = Field(default=60, description="API timeout")
    max_concurrent_requests: int = Field(default=5, description="Concurrent API calls")
    cache_duration_minutes: int = Field(default=15, description="Cache duration")
    screenshot_display_limit: int = Field(default=8, description="Max screenshots displayed")

class PartnerConfig(BaseModel):
    """Complete partner configuration."""
    partner: PartnerInfo
    datto: DattoConfig
    qbr: QBRConfig = Field(default_factory=QBRConfig)
    saas_pricing: SaaSPricing = Field(default_factory=SaaSPricing)
    dashboard: DashboardConfig = Field(default_factory=DashboardConfig)
    alerts: AlertConfig = Field(default_factory=AlertConfig)
    risk: RiskConfig = Field(default_factory=RiskConfig)
    recommendations: RecommendationsConfig = Field(default_factory=RecommendationsConfig)
    features: FeatureFlags = Field(default_factory=FeatureFlags)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)

def load_config(config_path: Optional[str] = None) -> PartnerConfig:
    """Load partner configuration from YAML file."""
    if config_path is None:
        # Look for config in standard locations
        config_locations = [
            "partner_config.yaml",
            "config/partner_config.yaml",
            os.path.expanduser("~/.datto-mcp/partner_config.yaml"),
            "/etc/datto-mcp/partner_config.yaml"
        ]
        
        for location in config_locations:
            if os.path.exists(location):
                config_path = location
                break
        
        if config_path is None:
            raise FileNotFoundError(
                "No configuration file found. Please create partner_config.yaml or specify path."
            )
    
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    # Load YAML with environment variable substitution
    with open(config_path, 'r') as f:
        yaml_content = f.read()
    
    # Simple environment variable substitution
    import re
    def replace_env_vars(match):
        var_name = match.group(1)
        return os.getenv(var_name, match.group(0))
    
    yaml_content = re.sub(r'\$\{([^}]+)\}', replace_env_vars, yaml_content)
    
    # Parse YAML
    config_data = yaml.safe_load(yaml_content)
    
    # Validate and create config object
    try:
        return PartnerConfig(**config_data)
    except Exception as e:
        raise ValueError(f"Invalid configuration: {e}")

def create_sample_config(output_path: str = "partner_config.yaml") -> None:
    """Create a sample configuration file."""
    sample_config = {
        "partner": {
            "name": "Your MSP Name",
            "initials": "MSP", 
            "support_email": "<EMAIL>",
            "emergency_phone": "1-800-YOUR-MSP",
            "website": "https://yourmsp.com",
            "logo_url": ""
        },
        "datto": {
            "public_key": "${DATTO_PUBLIC_KEY}",
            "secret_key": "${DATTO_SECRET_KEY}",
            "base_url": "https://api.datto.com",
            "rate_limit_budget": 10000
        }
    }
    
    with open(output_path, 'w') as f:
        yaml.dump(sample_config, f, default_flow_style=False, indent=2)
    
    print(f"Sample configuration created: {output_path}")
    print("Please edit this file with your partner details and Datto API credentials.")

# Global config instance
_config: Optional[PartnerConfig] = None

def get_config() -> PartnerConfig:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = load_config()
    return _config

def reload_config(config_path: Optional[str] = None) -> PartnerConfig:
    """Reload configuration from file."""
    global _config
    _config = load_config(config_path)
    return _config