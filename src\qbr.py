#!/usr/bin/env python3
"""
QBR (Quarterly Business Review) Generator
Token-efficient QBR generation with partner customization and realistic business impact calculations.
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

from config import PartnerConfig
from models import *

class QBRGenerator:
    """Generates comprehensive QBRs with partner branding and configuration."""
    
    def __init__(self, config: PartnerConfig, client):
        self.config = config
        self.client = client
        self.logger = logging.getLogger(f"{__name__}.QBRGenerator")
        
        # Load QBR template
        template_path = Path(__file__).parent.parent / "templates" / "qbr_template.html"
        if template_path.exists():
            with open(template_path, 'r') as f:
                self.qbr_template = f.read()
        else:
            self.logger.warning("QBR template not found, using basic template")
            self.qbr_template = self._get_basic_template()
    
    async def generate_qbr(
        self, 
        client_name: str, 
        quarter: Optional[str] = None,
        include_recovery_events: bool = True
    ) -> str:
        """Generate comprehensive QBR with partner branding."""
        self.logger.info(f"Generating QBR for {client_name}")
        
        # Determine quarter
        if quarter is None or quarter == "auto":
            current_quarter = f"Q{((datetime.now().month - 1) // 3) + 1}"
            quarter = f"{current_quarter} {datetime.now().year}"
        
        # Gather data from APIs
        devices_data = await self._get_devices_data()
        saas_data = await self._get_saas_data()
        recovery_events = await self._analyze_recovery_events() if include_recovery_events else []
        
        # Calculate metrics
        metrics = self._calculate_qbr_metrics(
            client_name=client_name,
            quarter=quarter,
            devices_data=devices_data,
            saas_data=saas_data,
            recovery_events=recovery_events
        )
        
        # Generate recommendations
        recommendations = self._generate_msp_recommendations(metrics)
        
        # Populate template
        qbr_html = self._populate_template(metrics, recommendations)
        
        self.logger.info(f"QBR generated successfully for {client_name}")
        return qbr_html
    
    async def _get_devices_data(self) -> Dict[str, Any]:
        """Get BCDR devices data."""
        try:
            result = await self.client.get("/v1/bcdr/device")
            devices = result.get('items', [])
            
            return {
                'total_devices': len(devices),
                'devices_with_alerts': len([d for d in devices if d.get('alertCount', 0) > 0]),
                'total_agents': sum(d.get('agentCount', 0) for d in devices),
                'total_shares': sum(d.get('shareCount', 0) for d in devices),
                'devices': devices
            }
        except Exception as e:
            self.logger.error(f"Failed to get devices data: {e}")
            return {'total_devices': 0, 'devices_with_alerts': 0, 'total_agents': 0, 'total_shares': 0, 'devices': []}
    
    async def _get_saas_data(self) -> Dict[str, Any]:
        """Get SaaS protection data."""
        try:
            domains_result = await self.client.get("/v1/saas/domains")
            domains = domains_result.get('items', [])
            
            total_seats = 0
            active_seats = 0
            
            for domain in domains:
                try:
                    seats_result = await self.client.get(f"/v1/saas/{domain.get('saasCustomerId')}/seats")
                    seats = seats_result.get('items', [])
                    total_seats += len(seats)
                    active_seats += len([s for s in seats if s.get('seatState') == 'Active'])
                except Exception:
                    continue
            
            return {
                'total_seats': total_seats,
                'active_seats': active_seats,
                'domains': domains
            }
        except Exception as e:
            self.logger.error(f"Failed to get SaaS data: {e}")
            return {'total_seats': 0, 'active_seats': 0, 'domains': []}
    
    async def _analyze_recovery_events(self) -> List[RecoveryEvent]:
        """Analyze activity log for recovery events."""
        recovery_events = []
        
        try:
            # Get activity log for last 90 days
            activity_result = await self.client.get("/v1/report/activity-log", params={
                'since': 90,
                'sinceUnits': 'days'
            })
            
            activities = activity_result.get('items', [])
            
            # Filter for recovery-related activities
            recovery_keywords = ['restore', 'recovery', 'recover', 'backup', 'snapshot', 'failover']
            urgent_keywords = ['failure', 'error', 'urgent', 'critical', 'emergency', 'outage']
            
            for activity in activities:
                message = activity.get('messageEN', '').lower()
                if any(keyword in message for keyword in recovery_keywords):
                    if any(urgent in message for urgent in urgent_keywords):
                        # This looks like a real recovery event
                        recovery_event = RecoveryEvent(
                            event_date=datetime.fromisoformat(activity.get('timestamp', datetime.now().isoformat())),
                            recovery_type=RecoveryType.HARDWARE_FAILURE,  # Default
                            affected_systems=[activity.get('targetDisplayName', 'Unknown System')],
                            downtime_hours=1.0,  # Estimated
                            employees_affected=25,  # Estimated
                            recovery_time_minutes=30,  # Estimated
                            business_impact_description=activity.get('messageEN', 'System recovery event')
                        )
                        recovery_events.append(recovery_event)
        
        except Exception as e:
            self.logger.error(f"Failed to analyze recovery events: {e}")
        
        return recovery_events[:5]  # Limit to 5 most recent
    
    def _calculate_qbr_metrics(
        self,
        client_name: str,
        quarter: str,
        devices_data: Dict,
        saas_data: Dict,
        recovery_events: List[RecoveryEvent]
    ) -> QBRMetrics:
        """Calculate comprehensive QBR metrics."""
        
        # Basic metrics
        total_devices = devices_data.get('total_devices', 0)
        devices_with_alerts = devices_data.get('devices_with_alerts', 0)
        uptime_percentage = max(0, 100 - (devices_with_alerts / max(total_devices, 1) * 10))
        
        # Estimate data protected (rough calculation)
        total_data_gb = (devices_data.get('total_agents', 0) * 200) + (devices_data.get('total_shares', 0) * 500)
        
        # Calculate costs avoided
        downtime_cost_avoided = 0
        ransom_cost_avoided = 0
        
        for event in recovery_events:
            if event.recovery_type == RecoveryType.RANSOMWARE:
                impact = self._calculate_ransomware_impact(
                    event.ransom_demand or 220000,  # Default demand
                    event.downtime_hours,
                    event.employees_affected
                )
                ransom_cost_avoided += impact['total_impact']
            else:
                downtime_cost_avoided += self._calculate_downtime_cost(
                    event.downtime_hours,
                    event.employees_affected
                )
        
        # Compliance value
        total_users = max(saas_data.get('total_seats', 0), 25)  # Minimum estimate
        compliance_value = total_users * self.config.qbr.cost_calculations.compliance_value_per_user / 4  # Quarterly
        
        # Service cost estimation
        monthly_cost = (total_devices * 150) + (total_users * 8)
        
        # ROI calculation
        total_value = downtime_cost_avoided + ransom_cost_avoided + compliance_value
        quarterly_investment = monthly_cost * 3
        roi_percentage = ((total_value - quarterly_investment) / max(quarterly_investment, 1)) * 100
        
        return QBRMetrics(
            client_name=client_name,
            quarter=quarter,
            total_devices=total_devices,
            total_users=total_users,
            total_data_protected_gb=total_data_gb / 1000,  # Convert to TB
            uptime_percentage=round(uptime_percentage, 1),
            successful_backups=1000,  # Estimated
            failed_backups=devices_with_alerts * 5,  # Estimated
            recovery_events=recovery_events,
            monthly_service_cost=monthly_cost,
            downtime_cost_avoided=downtime_cost_avoided,
            ransom_cost_avoided=ransom_cost_avoided,
            compliance_value=compliance_value,
            total_roi=roi_percentage
        )
    
    def _calculate_downtime_cost(self, hours: float, employees: int) -> float:
        """Calculate downtime cost using configured business size thresholds."""
        cost_config = self.config.qbr.cost_calculations
        
        if employees <= 100:
            hourly_cost = cost_config.small_business_downtime_cost
        elif employees <= 1000:
            hourly_cost = cost_config.mid_market_downtime_cost
        else:
            hourly_cost = cost_config.enterprise_downtime_cost
        
        # Scale by actual employees
        base_employees = 50 if employees <= 100 else (250 if employees <= 1000 else 2000)
        scaling_factor = min(employees / base_employees, 2.0)
        
        return hours * hourly_cost * scaling_factor
    
    def _calculate_ransomware_impact(self, ransom_demand: float, downtime_hours: float, employees: int) -> Dict[str, float]:
        """Calculate total ransomware impact using industry benchmarks."""
        # Total cost is typically 4.62x the ransom demand
        total_ransomware_cost = ransom_demand * 4.62
        
        # Add downtime costs
        downtime_cost = self._calculate_downtime_cost(downtime_hours, employees)
        
        return {
            'ransom_demand': ransom_demand,
            'total_ransomware_cost': total_ransomware_cost,
            'downtime_cost': downtime_cost,
            'total_impact': total_ransomware_cost + downtime_cost
        }
    
    def calculate_business_impact(
        self,
        recovery_type: str,
        downtime_hours: float,
        employees_affected: int,
        ransom_demand: Optional[float] = None
    ) -> Dict[str, Any]:
        """Calculate business impact for a specific scenario."""
        
        if recovery_type.lower() == 'ransomware':
            if not ransom_demand:
                ransom_demand = 220000  # Industry average
            
            impact = self._calculate_ransomware_impact(ransom_demand, downtime_hours, employees_affected)
            
            return {
                "recovery_type": "Ransomware Attack",
                "ransom_demand": f"${impact['ransom_demand']:,.0f}",
                "total_ransomware_cost": f"${impact['total_ransomware_cost']:,.0f}",
                "downtime_cost": f"${impact['downtime_cost']:,.0f}",
                "total_business_impact": f"${impact['total_impact']:,.0f}",
                "partner_saved_client": f"${impact['total_impact']:,.0f}",
                "calculation_notes": [
                    f"Ransom demand: ${impact['ransom_demand']:,.0f}",
                    f"Total ransomware costs (4.62x multiplier): ${impact['total_ransomware_cost']:,.0f}",
                    f"Downtime costs ({downtime_hours} hours): ${impact['downtime_cost']:,.0f}",
                    f"Total business impact avoided by {self.config.partner.name} backup recovery"
                ]
            }
        else:
            downtime_cost = self._calculate_downtime_cost(downtime_hours, employees_affected)
            
            return {
                "recovery_type": recovery_type.replace('_', ' ').title(),
                "downtime_hours": downtime_hours,
                "employees_affected": employees_affected,
                "downtime_cost": f"${downtime_cost:,.0f}",
                "partner_saved_client": f"${downtime_cost:,.0f}",
                "calculation_notes": [
                    f"Business impact based on {employees_affected} employees affected",
                    f"Downtime cost: {downtime_hours} hours × industry benchmark",
                    f"Total cost avoided through {self.config.partner.name} backup recovery"
                ]
            }
    
    def _generate_msp_recommendations(self, metrics: QBRMetrics) -> List[MSPRecommendation]:
        """Generate MSP-focused recommendations with partner branding."""
        recommendations = []
        
        # Server refresh opportunity
        if metrics.total_devices > 3:
            recommendations.append(MSPRecommendation(
                priority="High Priority",
                title="Infrastructure Health Assessment",
                description=f"Comprehensive review of {metrics.total_devices} protected systems to identify aging hardware and optimization opportunities. Proactive replacement prevents unplanned downtime.",
                value_proposition=f"Potential savings: ${metrics.total_devices * self.config.recommendations.server_refresh_cost_per_server:,} annually in avoided downtime",
                category="Infrastructure",
                implementation_effort="Medium"
            ))
        
        # SaaS optimization
        if metrics.total_users > 10:
            recommendations.append(MSPRecommendation(
                priority="Medium Priority",
                title="SaaS License Optimization Program",
                description=f"Audit and optimize {metrics.total_users} SaaS seats to eliminate waste and right-size licensing. Ongoing monitoring prevents future waste.",
                value_proposition=f"Estimated monthly savings: ${metrics.total_users * 3:,} through license optimization",
                category="Cost Optimization",
                implementation_effort="Low"
            ))
        
        # Security enhancement (if recovery events exist)
        if metrics.recovery_events:
            recommendations.append(MSPRecommendation(
                priority="Critical",
                title="Advanced Security Stack Implementation",
                description=f"Based on {len(metrics.recovery_events)} recovery events this quarter, implement enhanced security including EDR, email protection, and security awareness training.",
                value_proposition=f"Risk mitigation value: ${self.config.recommendations.security_stack_annual_savings:,} annually in prevented incidents",
                category="Security",
                implementation_effort="High"
            ))
        
        # Compliance automation
        if metrics.total_users > 25:
            recommendations.append(MSPRecommendation(
                priority="Strategic",
                title="Compliance Automation Platform",
                description=f"Implement automated compliance monitoring for {metrics.total_users} users to streamline audit processes and reduce compliance overhead.",
                value_proposition=f"Time savings: {self.config.recommendations.compliance_automation_hours_saved} hours quarterly in compliance activities",
                category="Compliance",
                implementation_effort="Medium"
            ))
        
        return recommendations
    
    def _populate_template(self, metrics: QBRMetrics, recommendations: List[MSPRecommendation]) -> str:
        """Populate QBR template with metrics and partner branding."""
        
        # Calculate additional metrics
        total_value_delivered = metrics.downtime_cost_avoided + metrics.ransom_cost_avoided + metrics.compliance_value
        quarterly_investment = metrics.monthly_service_cost * 3
        
        # Build recommendations HTML
        recommendations_html = ""
        for rec in recommendations:
            recommendations_html += f"""
            <div class="rec-item">
                <div class="rec-priority">{rec.priority}</div>
                <div class="rec-title">{rec.title}</div>
                <div class="rec-description">{rec.description}</div>
                <div class="rec-value">{rec.value_proposition}</div>
            </div>
            """
        
        # Build recovery events HTML
        recovery_events_html = ""
        if metrics.recovery_events:
            for event in metrics.recovery_events:
                if event.recovery_type == RecoveryType.RANSOMWARE:
                    impact_cost = self._calculate_ransomware_impact(
                        event.ransom_demand or 220000,
                        event.downtime_hours,
                        event.employees_affected
                    )['total_impact']
                else:
                    impact_cost = self._calculate_downtime_cost(event.downtime_hours, event.employees_affected)
                
                recovery_events_html += f"""
                <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 4px solid #28a745;">
                    <h3 style="color: #28a745; margin-bottom: 15px;">✅ {event.event_date.strftime('%B %d')}: System Recovery</h3>
                    <p style="color: #666; margin-bottom: 10px;"><strong>Issue:</strong> {event.business_impact_description}</p>
                    <p style="color: #666; margin-bottom: 10px;"><strong>Resolution:</strong> Restored by {self.config.partner.name} within {event.recovery_time_minutes} minutes</p>
                    <p style="color: #666; margin-bottom: 15px;"><strong>Business Impact:</strong> Prevented ${impact_cost:,.0f} in business disruption</p>
                    <div style="background: #d4edda; padding: 10px; border-radius: 8px; color: #155724; font-weight: 600;">
                        Recovery Time: {event.recovery_time_minutes} minutes | Data Loss: {event.data_loss_gb}GB ✅
                    </div>
                </div>
                """
        else:
            recovery_events_html = """
            <div style="text-align: center; color: #666; padding: 40px;">
                No critical recovery events this quarter - excellent system stability maintained by {partner_name}!
            </div>
            """.format(partner_name=self.config.partner.name)
        
        # Calculate next QBR date
        current_date = datetime.now()
        current_quarter = ((current_date.month - 1) // 3) + 1
        next_quarter = current_quarter + 1 if current_quarter < 4 else 1
        next_year = current_date.year if next_quarter > current_quarter else current_date.year + 1
        next_qbr_date = f"{['', 'February', 'May', 'August', 'November'][next_quarter]} {next_year}"
        
        # Asset portfolio
        asset_portfolio_html = f"""
        <div class="asset-item">
            <div class="asset-name">{metrics.client_name.upper().replace(' ', '-')}-DC-01</div>
            <div class="asset-type">Domain Controller</div>
            <div class="asset-metrics">
                <div class="metric-item">
                    <div class="metric-value">{metrics.total_data_protected_gb * 0.2:.0f}GB</div>
                    <div class="metric-label">Data Volume</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">{100 if metrics.uptime_percentage > 99 else 98}%</div>
                    <div class="metric-label">Success Rate</div>
                </div>
            </div>
        </div>
        <div class="asset-item">
            <div class="asset-name">Office 365 Tenant</div>
            <div class="asset-type">SaaS Platform</div>
            <div class="asset-metrics">
                <div class="metric-item">
                    <div class="metric-value">{metrics.total_data_protected_gb * 0.15:.0f}GB</div>
                    <div class="metric-label">Data Volume</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">{(metrics.successful_backups / max(metrics.successful_backups + metrics.failed_backups, 1)) * 100:.1f}%</div>
                    <div class="metric-label">Success Rate</div>
                </div>
            </div>
        </div>
        """
        
        # Populate template
        return self.qbr_template.format(
            CLIENT_NAME=metrics.client_name,
            MSP_INITIALS=self.config.partner.initials,
            MSP_NAME=self.config.partner.name,
            QUARTER=metrics.quarter,
            REPORT_DATE=datetime.now().strftime('%B %d, %Y'),
            UPTIME_PERCENTAGE=metrics.uptime_percentage,
            PROTECTED_USERS=metrics.total_users,
            TOTAL_VALUE_DELIVERED=f"${total_value_delivered:,.0f}",
            CRITICAL_RECOVERIES=len(metrics.recovery_events),
            DOWNTIME_AVOIDED=f"${metrics.downtime_cost_avoided:,.0f}",
            SCREENSHOT_GALLERY="<div class=\"no-screenshot\">Screenshot verification data available via partner portal</div>",
            # Service metrics
            SAAS_STATUS_CLASS="warning-indicator" if metrics.failed_backups > 0 else "",
            SAAS_STATUS_TEXT=f"Performance: {((metrics.successful_backups / max(metrics.successful_backups + metrics.failed_backups, 1)) * 100):.1f}% Success Rate",
            SAAS_BADGE_TEXT="Action Plan Ready" if metrics.failed_backups > 0 else "SLA Exceeded",
            SAAS_USER_SEATS=metrics.total_users,
            SAAS_SERVICES_PROTECTED=metrics.total_users + 15,
            SAAS_SUCCESSFUL_BACKUPS=metrics.successful_backups,
            SAAS_DATA_PROTECTED=f"{metrics.total_data_protected_gb * 0.15:.0f}GB",
            # BCDR metrics
            BCDR_STATUS_CLASS="" if metrics.uptime_percentage > 99 else "warning-indicator",
            BCDR_STATUS_TEXT=f"Excellent Performance: {metrics.uptime_percentage:.1f}% Availability",
            BCDR_BADGE_TEXT="SLA Exceeded",
            BCDR_PROTECTED_ASSETS=metrics.total_devices,
            BCDR_DATA_VOLUME=f"{metrics.total_data_protected_gb:.1f}TB",
            BCDR_RECOVERY_RTO="15min",
            # Asset portfolio and content
            ASSET_PORTFOLIO=asset_portfolio_html,
            QUARTERLY_INVESTMENT=f"${quarterly_investment:,.0f}",
            ROI_PERCENTAGE=int(metrics.total_roi),
            COMPLIANCE_VALUE=f"${metrics.compliance_value:,.0f}",
            TIME_SAVED="48hrs",
            RECOVERY_EVENTS=recovery_events_html,
            RECOMMENDATIONS=recommendations_html,
            # Partner footer info
            NEXT_QBR_DATE=next_qbr_date,
            MSP_SUPPORT_EMAIL=self.config.partner.support_email,
            MSP_EMERGENCY_PHONE=self.config.partner.emergency_phone,
            TIMESTAMP=datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')
        )
    
    def _get_basic_template(self) -> str:
        """Fallback basic template if main template not found."""
        return """
        <!DOCTYPE html>
        <html>
        <head><title>{CLIENT_NAME} - Quarterly Business Review</title></head>
        <body>
        <h1>{CLIENT_NAME} - {QUARTER} QBR</h1>
        <p>Generated by {MSP_NAME}</p>
        <h2>Key Metrics</h2>
        <ul>
            <li>Uptime: {UPTIME_PERCENTAGE}%</li>
            <li>Protected Users: {PROTECTED_USERS}</li>
            <li>Value Delivered: {TOTAL_VALUE_DELIVERED}</li>
            <li>ROI: {ROI_PERCENTAGE}%</li>
        </ul>
        <h2>Recovery Events</h2>
        {RECOVERY_EVENTS}
        <h2>Recommendations</h2>
        {RECOMMENDATIONS}
        <p>Contact: {MSP_SUPPORT_EMAIL} | Emergency: {MSP_EMERGENCY_PHONE}</p>
        </body>
        </html>
        """