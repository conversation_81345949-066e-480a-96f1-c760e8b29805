#!/usr/bin/env python3
"""
Basic tests for Datto Partner Portal MCP Server
Run with: python -m pytest tests/
"""

import os
import sys
import pytest
import yaml
from unittest.mock import Mock, patch

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from utils.formatting import format_storage_size, format_currency, format_timestamp
from utils.api_client import DattoConfig
from qbr.metrics import BusinessImpactCalculator


class TestFormatting:
    """Test formatting utilities."""
    
    def test_format_storage_size(self):
        """Test storage size formatting."""
        assert format_storage_size(500) == "500GB"
        assert format_storage_size(1024) == "1.0TB"
        assert format_storage_size(2500) == "2.4TB"
        assert format_storage_size(0) == "0GB"
    
    def test_format_currency(self):
        """Test currency formatting."""
        assert format_currency(1000) == "$1,000.00"
        assert format_currency(1234.56) == "$1,234.56"
        assert format_currency(0) == "$0.00"
    
    def test_format_timestamp(self):
        """Test timestamp formatting."""
        # Test with Unix timestamp
        timestamp = 1640995200  # 2022-01-01 00:00:00 UTC
        result = format_timestamp(timestamp)
        assert "/" in result  # Should contain date separator
        
        # Test with zero timestamp
        assert format_timestamp(0) == "No recent backup"
        
        # Test with invalid timestamp
        assert format_timestamp("invalid") == "Invalid date"


class TestDattoConfig:
    """Test Datto API configuration."""
    
    def test_config_creation(self):
        """Test configuration object creation."""
        config = DattoConfig(
            public_key="test_public",
            secret_key="test_secret"
        )
        assert config.public_key == "test_public"
        assert config.secret_key == "test_secret"
        assert config.base_url == "https://api.datto.com"
        assert config.timeout_seconds == 60


class TestBusinessImpactCalculator:
    """Test business impact calculations."""
    
    def setup_method(self):
        """Setup test configuration."""
        self.test_config = {
            'qbr': {
                'business_impact': {
                    'employee_hourly_cost': 75.0,
                    'productivity_loss_multiplier': 0.85,
                    'it_staff_hourly_cost': 125.0,
                    'average_incident_response_hours': 4.0,
                    'ransomware_cost_multiplier': 4.62,
                    'default_ransom_demand': 220000.0
                },
                'compliance': {
                    'hipaa_violation_average': 2300000,
                    'pci_violation_average': 190000
                }
            }
        }
        self.calculator = BusinessImpactCalculator(self.test_config)
    
    def test_business_size_calculation(self):
        """Test business size categorization."""
        assert self.calculator.calculate_business_size(50) == "small_business"
        assert self.calculator.calculate_business_size(500) == "mid_market"
        assert self.calculator.calculate_business_size(2000) == "enterprise"
    
    def test_downtime_cost_calculation(self):
        """Test downtime cost calculation."""
        # Test with 100 employees for 2 hours
        cost = self.calculator.calculate_downtime_cost(2.0, 100)
        assert cost > 0
        assert isinstance(cost, float)
    
    def test_ransomware_impact_calculation(self):
        """Test ransomware impact calculation."""
        impact = self.calculator.calculate_ransomware_impact(100000, 24, 100)
        
        assert 'ransom_demand' in impact
        assert 'total_ransomware_cost' in impact
        assert 'downtime_cost' in impact
        assert 'total_impact' in impact
        
        # Total cost should be more than ransom alone
        assert impact['total_impact'] > impact['ransom_demand']
    
    def test_compliance_value_calculation(self):
        """Test compliance value calculation."""
        value = self.calculator.calculate_compliance_value(100)
        assert value > 0
        assert isinstance(value, float)


class TestConfiguration:
    """Test configuration file handling."""
    
    def test_example_config_valid(self):
        """Test that example configuration is valid YAML."""
        config_path = os.path.join(
            os.path.dirname(__file__), '..', 'config', 'config-example.yaml'
        )
        
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Verify required sections exist
        assert 'api' in config
        assert 'performance' in config
        assert 'qbr' in config
        
        # Verify required API fields
        assert 'public_key' in config['api']
        assert 'secret_key' in config['api']


class TestIntegration:
    """Integration tests (require network/API access)."""
    
    @pytest.mark.integration
    def test_api_connection(self):
        """Test API connection (requires valid credentials)."""
        # This test should be skipped unless running integration tests
        pytest.skip("Integration test - requires valid API credentials")


if __name__ == "__main__":
    pytest.main([__file__])