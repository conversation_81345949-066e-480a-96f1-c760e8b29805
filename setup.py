#!/usr/bin/env python3
"""
Datto Partner MCP Server Setup Script
Automated setup and configuration for partners.
"""

import os
import sys
import shutil
import subprocess
import yaml
from pathlib import Path
from typing import Dict, Any

def print_header():
    """Print setup header."""
    print("🛡️  Datto Partner MCP Server Setup")
    print("=" * 50)
    print("Setting up your intelligent backup monitoring assistant...\n")

def check_requirements():
    """Check system requirements."""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required. Current version:", sys.version)
        return False
    print("✅ Python version:", sys.version.split()[0])
    
    # Check pip
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip is available")
    except subprocess.CalledProcessError:
        print("❌ pip is not available")
        return False
    
    return True

def create_virtual_environment():
    """Create and activate virtual environment."""
    print("\n🐍 Setting up virtual environment...")
    
    venv_path = Path("venv")
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    try:
        subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        print("✅ Virtual environment created")
        
        # Determine activation script path
        if os.name == 'nt':  # Windows
            activate_script = venv_path / "Scripts" / "activate"
            pip_executable = venv_path / "Scripts" / "pip"
        else:  # Unix/Linux/macOS
            activate_script = venv_path / "bin" / "activate"
            pip_executable = venv_path / "bin" / "pip"
        
        print(f"💡 To activate: source {activate_script}")
        return True
        
    except subprocess.CalledProcessError:
        print("❌ Failed to create virtual environment")
        return False

def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    
    # Determine pip path
    venv_path = Path("venv")
    if os.name == 'nt':  # Windows
        pip_executable = venv_path / "Scripts" / "pip"
    else:  # Unix/Linux/macOS  
        pip_executable = venv_path / "bin" / "pip"
    
    if not pip_executable.exists():
        print("⚠️  Virtual environment not found, using system pip")
        pip_executable = "pip"
    
    try:
        subprocess.run([str(pip_executable), "install", "-r", "requirements.txt"], 
                      check=True)
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False

def get_partner_info() -> Dict[str, Any]:
    """Collect partner information interactively."""
    print("\n🏢 Partner Information Setup")
    print("Please provide your MSP/Partner details:\n")
    
    partner_info = {}
    
    # Required fields
    partner_info['name'] = input("Company/MSP Name: ").strip()
    while not partner_info['name']:
        partner_info['name'] = input("Company/MSP Name (required): ").strip()
    
    # Generate initials from name
    words = partner_info['name'].split()
    suggested_initials = ''.join([w[0].upper() for w in words[:3]])
    initials_input = input(f"Company Initials [{suggested_initials}]: ").strip()
    partner_info['initials'] = initials_input if initials_input else suggested_initials
    
    partner_info['support_email'] = input("Support Email: ").strip()
    while not partner_info['support_email'] or '@' not in partner_info['support_email']:
        partner_info['support_email'] = input("Support Email (required, valid email): ").strip()
    
    partner_info['emergency_phone'] = input("Emergency Phone (24/7): ").strip()
    partner_info['website'] = input("Company Website (optional): ").strip()
    partner_info['logo_url'] = input("Logo URL (optional): ").strip()
    
    return partner_info

def get_api_credentials() -> Dict[str, str]:
    """Collect Datto API credentials."""
    print("\n🔑 Datto API Credentials")
    print("Get these from your Datto Partner Portal:\n")
    
    credentials = {}
    
    credentials['public_key'] = input("Datto API Public Key: ").strip()
    while not credentials['public_key']:
        credentials['public_key'] = input("Datto API Public Key (required): ").strip()
    
    credentials['secret_key'] = input("Datto API Secret Key: ").strip()
    while not credentials['secret_key']:
        credentials['secret_key'] = input("Datto API Secret Key (required): ").strip()
    
    return credentials

def get_feature_preferences() -> Dict[str, bool]:
    """Get feature preferences."""
    print("\n⚙️  Feature Configuration")
    print("Enable intelligent features (y/n):\n")
    
    features = {}
    
    feature_descriptions = {
        'intelligent_alert_correlation': 'AI-powered alert grouping and root cause analysis',
        'predictive_risk_assessment': 'ML-based backup failure prediction',
        'saas_license_optimization': 'SaaS license waste identification',
        'screenshot_verification': 'Visual backup verification monitoring',
        'interactive_qbr': 'Automated quarterly business review generation',
        'business_impact_calculations': 'Realistic business impact cost calculations'
    }
    
    for feature, description in feature_descriptions.items():
        response = input(f"Enable {description}? [Y/n]: ").strip().lower()
        features[feature] = response in ['', 'y', 'yes']
    
    return features

def create_configuration(partner_info: Dict, credentials: Dict, features: Dict):
    """Create partner configuration file."""
    print("\n📝 Creating configuration file...")
    
    config = {
        'partner': {
            'name': partner_info['name'],
            'initials': partner_info['initials'],
            'support_email': partner_info['support_email'],
            'emergency_phone': partner_info['emergency_phone'],
            'website': partner_info['website'],
            'logo_url': partner_info['logo_url']
        },
        'datto': {
            'public_key': '${DATTO_PUBLIC_KEY}',
            'secret_key': '${DATTO_SECRET_KEY}',
            'base_url': 'https://api.datto.com',
            'rate_limit_budget': 10000
        },
        'qbr': {
            'default_quarter': 'auto',
            'include_screenshots': True,
            'cost_calculations': {
                'small_business_downtime_cost': 8600,
                'mid_market_downtime_cost': 35000,
                'enterprise_downtime_cost': 100000,
                'compliance_value_per_user': 150,
                'audit_cost_avoided': 25000,
                'fine_risk_mitigation': 50000
            }
        },
        'saas_pricing': {
            'office365': {
                'user': 4.0,
                'shared_mailbox': 2.0,
                'site': 1.0,
                'team': 1.0
            },
            'google_workspace': {
                'user': 3.0,
                'shared_drive': 1.0,
                'team': 1.0
            }
        },
        'dashboard': {
            'theme': 'dark',
            'company_colors': {
                'primary': '#667eea',
                'secondary': '#764ba2',
                'accent': '#60a5fa'
            },
            'refresh_interval': 300
        },
        'alerts': {
            'correlation_window_hours': 4,
            'noise_reduction_threshold': 2,
            'critical_device_threshold': 3
        },
        'risk': {
            'high_risk_threshold': 70,
            'medium_risk_threshold': 40,
            'backup_age_warning_hours': 24,
            'backup_age_critical_hours': 48,
            'screenshot_failure_risk_points': 25
        },
        'recommendations': {
            'server_refresh_cost_per_server': 15000,
            'storage_expansion_performance_gain': 25,
            'security_stack_annual_savings': 12000,
            'compliance_automation_hours_saved': 40
        },
        'features': features,
        'logging': {
            'level': 'INFO',
            'file': 'logs/datto_mcp.log',
            'max_size_mb': 10,
            'backup_count': 5
        },
        'performance': {
            'api_timeout_seconds': 60,
            'max_concurrent_requests': 5,
            'cache_duration_minutes': 15,
            'screenshot_display_limit': 8
        }
    }
    
    # Create config directory
    config_path = Path("config")
    config_path.mkdir(exist_ok=True)
    
    # Write configuration
    with open(config_path / "partner_config.yaml", 'w') as f:
        yaml.dump(config, f, default_flow_style=False, indent=2)
    
    print("✅ Configuration file created: config/partner_config.yaml")
    
    # Create environment file
    env_content = f"""# Datto Partner MCP Server Environment Variables
# Auto-generated by setup script

# Datto API Credentials
DATTO_PUBLIC_KEY={credentials['public_key']}
DATTO_SECRET_KEY={credentials['secret_key']}

# Optional: Override default settings
# MCP_LOG_LEVEL=INFO
# MCP_CONFIG_PATH=config/partner_config.yaml
"""
    
    with open(".env", 'w') as f:
        f.write(env_content)
    
    print("✅ Environment file created: .env")
    print("⚠️  Keep your .env file secure and never commit it to version control!")

def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")
    
    directories = ["logs", "data", "temp"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created: {directory}/")

def create_client_config(partner_info: Dict):
    """Create sample Claude Desktop configuration."""
    print("\n🤖 Creating Claude Desktop configuration...")
    
    current_path = Path.cwd()
    claude_config = {
        "mcpServers": {
            f"datto-{partner_info['initials'].lower()}": {
                "command": "python",
                "args": [str(current_path / "src" / "datto_mcp.py")],
                "env": {
                    "DATTO_PUBLIC_KEY": "${DATTO_PUBLIC_KEY}",
                    "DATTO_SECRET_KEY": "${DATTO_SECRET_KEY}"
                }
            }
        }
    }
    
    examples_path = Path("examples")
    examples_path.mkdir(exist_ok=True)
    
    with open(examples_path / "claude_desktop_config.json", 'w') as f:
        import json
        json.dump(claude_config, f, indent=2)
    
    print("✅ Claude Desktop config created: examples/claude_desktop_config.json")

def test_installation():
    """Test the installation."""
    print("\n🧪 Testing installation...")
    
    try:
        # Test configuration loading
        sys.path.insert(0, 'src')
        from config import load_config
        config = load_config()
        print("✅ Configuration loads successfully")
        
        # Test basic imports
        from models import AlertAnalysis, QBRMetrics
        print("✅ Core models import successfully")
        
        print("✅ Installation test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False

def print_next_steps(partner_info: Dict):
    """Print next steps for the user."""
    print("\n🎉 Setup Complete!")
    print("=" * 50)
    
    print(f"\n✅ Your {partner_info['name']} MCP server is ready!")
    
    print("\n📋 Next Steps:")
    print("1. Activate virtual environment:")
    if os.name == 'nt':  # Windows
        print("   venv\\Scripts\\activate")
    else:  # Unix/Linux/macOS
        print("   source venv/bin/activate")
    
    print("\n2. Test the server:")
    print("   python src/datto_mcp.py")
    
    print("\n3. Configure Claude Desktop:")
    print("   Copy examples/claude_desktop_config.json to your Claude Desktop config")
    print("   Location: ~/.config/claude-desktop/config.json")
    
    print("\n4. Start using intelligent features:")
    print("   - analyze_backup_health()")
    print("   - correlate_alerts()")
    print("   - create_qbr(client_name='Your Client')")
    
    print("\n📚 Documentation:")
    print("   - README.md - Complete overview")
    print("   - docs/API_REFERENCE.md - All tools and resources")
    print("   - docs/DEPLOYMENT.md - Production deployment")
    
    print(f"\n🛟 Support:")
    print(f"   - Partner Support: {partner_info['support_email']}")
    print("   - Documentation: https://docs.datto.com/partners/mcp")
    print("   - Issues: GitHub repository")

def main():
    """Main setup flow."""
    print_header()
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Setup failed due to missing requirements")
        sys.exit(1)
    
    # Setup virtual environment
    if not create_virtual_environment():
        print("\n❌ Setup failed during virtual environment creation")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Setup failed during dependency installation")
        sys.exit(1)
    
    # Collect information
    partner_info = get_partner_info()
    credentials = get_api_credentials()
    features = get_feature_preferences()
    
    # Create configuration
    create_configuration(partner_info, credentials, features)
    
    # Create directories
    create_directories()
    
    # Create client config
    create_client_config(partner_info)
    
    # Test installation
    if not test_installation():
        print("\n⚠️  Setup completed with warnings - check configuration")
    
    # Print next steps
    print_next_steps(partner_info)

if __name__ == "__main__":
    main()