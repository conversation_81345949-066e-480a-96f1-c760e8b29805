# Datto Partner MCP Configuration
# Copy this file to partner_config.yaml and customize for your organization

# Partner/MSP Information
partner:
  name: "Your MSP Name"                    # Full company name
  initials: "MSP"                         # 2-3 letter abbreviation for logos
  support_email: "<EMAIL>"    # Primary support contact
  emergency_phone: "1-800-YOUR-MSP"       # 24/7 emergency number
  website: "https://yourmsp.com"          # Company website
  logo_url: ""                            # Optional: URL to company logo

# Datto API Configuration  
datto:
  # Get these from your Datto Partner Portal
  public_key: "${DATTO_PUBLIC_KEY}"       # Environment variable recommended
  secret_key: "${DATTO_SECRET_KEY}"       # Environment variable recommended
  base_url: "https://api.datto.com"       # Default Datto API endpoint
  rate_limit_budget: 10000                # API calls per hour

# QBR Customization
qbr:
  default_quarter: "auto"                 # "auto" or specific like "Q1 2025"
  include_screenshots: true               # Include screenshot verification section
  cost_calculations:
    # Customize business impact calculations
    small_business_downtime_cost: 8600    # $/hour for 1-100 employees
    mid_market_downtime_cost: 35000       # $/hour for 100-1000 employees  
    enterprise_downtime_cost: 100000      # $/hour for 1000+ employees
    compliance_value_per_user: 150        # $/user/year compliance value
    audit_cost_avoided: 25000             # Annual audit cost mitigation
    fine_risk_mitigation: 50000           # Potential fine mitigation value

# SaaS Protection Pricing (for license optimization)
saas_pricing:
  office365:
    user: 4.0                            # $/month per user seat
    shared_mailbox: 2.0                  # $/month per shared mailbox
    site: 1.0                            # $/month per SharePoint site
    team: 1.0                            # $/month per Teams
  google_workspace:
    user: 3.0                            # $/month per user seat
    shared_drive: 1.0                    # $/month per shared drive
    team: 1.0                            # $/month per team space

# Dashboard Customization
dashboard:
  theme: "dark"                          # "dark" or "light"
  company_colors:
    primary: "#667eea"                   # Primary brand color
    secondary: "#764ba2"                 # Secondary brand color
    accent: "#60a5fa"                    # Accent color for highlights
  refresh_interval: 300                  # Seconds between auto-refresh

# Alert Correlation Settings
alerts:
  correlation_window_hours: 4            # Group alerts within X hours
  noise_reduction_threshold: 2           # Minimum alerts to create correlation
  critical_device_threshold: 3           # Devices affected = critical severity
  
# Risk Assessment Configuration  
risk:
  high_risk_threshold: 70               # Risk score 70+ = high risk
  medium_risk_threshold: 40             # Risk score 40-69 = medium risk
  backup_age_warning_hours: 24          # Warn if backup older than X hours
  backup_age_critical_hours: 48         # Critical if backup older than X hours
  screenshot_failure_risk_points: 25    # Risk points for screenshot failure

# Recommendation Templates
recommendations:
  # Customize MSP upselling recommendations
  server_refresh_cost_per_server: 15000 # Annual downtime cost per old server
  storage_expansion_performance_gain: 25 # % performance improvement
  security_stack_annual_savings: 12000  # Annual savings from security incidents
  compliance_automation_hours_saved: 40 # Hours saved per quarter

# Feature Flags
features:
  intelligent_alert_correlation: true    # Enable AI-powered alert grouping
  predictive_risk_assessment: true       # Enable backup failure prediction
  saas_license_optimization: true        # Enable SaaS waste identification  
  screenshot_verification: true          # Include screenshot galleries
  interactive_qbr: true                  # Enable QBR generation
  business_impact_calculations: true     # Include realistic cost calculations

# Logging and Monitoring
logging:
  level: "INFO"                         # DEBUG, INFO, WARNING, ERROR
  file: "logs/datto_mcp.log"            # Log file path
  max_size_mb: 10                       # Max log file size before rotation
  backup_count: 5                       # Number of backup log files

# Performance Settings
performance:
  api_timeout_seconds: 60               # API request timeout
  max_concurrent_requests: 5            # Concurrent API calls
  cache_duration_minutes: 15            # Cache API responses for X minutes
  screenshot_display_limit: 16           # Max screenshots in dashboard