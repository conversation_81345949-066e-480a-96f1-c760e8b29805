# Datto Partner Portal MCP Configuration
# Copy from config-example.yaml and customize for your environment

# API Configuration
api:
  public_key: "${DATTO_PUBLIC_KEY}"
  secret_key: "${DATTO_SECRET_KEY}"
  base_url: "https://api.datto.com"
  timeout_seconds: 60
  rate_limit_budget: 10000

# Performance Settings
performance:
  max_devices_per_query: 50
  max_assets_per_device: 10
  max_dtc_assets: 50
  max_saas_domains: 20
  screenshot_limit: 8
  backup_history_days: 30
  backup_history_limit: 100

# QBR Business Impact Calculations
qbr:
  business_impact:
    # Hourly employee cost for downtime calculations
    employee_hourly_cost: 75.00
    
    # Productivity multiplier during outages
    productivity_loss_multiplier: 0.85
    
    # IT staff hourly cost for incident response
    it_staff_hourly_cost: 125.00
    
    # Average IT staff time per incident (hours)
    average_incident_response_hours: 4.0
    
    # Ransomware cost multiplier (industry average 4.62x ransom demand)
    ransomware_cost_multiplier: 4.62
    
    # Default ransom demand if not specified
    default_ransom_demand: 220000.00

  recovery_times:
    # Average recovery time estimates by incident type (minutes)
    hardware_failure: 240
    ransomware: 720
    human_error: 60
    software_corruption: 180
    natural_disaster: 1440
    cyber_attack: 480

  storage_costs:
    # Storage cost per GB per month for ROI calculations
    cost_per_gb_month: 0.12
    
    # Backup storage efficiency ratio
    deduplication_ratio: 3.5
    
    # Alternative storage cost (if no Datto protection)
    alternative_storage_cost_multiplier: 1.8

  compliance:
    # Compliance violation costs by regulation type
    hipaa_violation_average: 2300000
    pci_violation_average: 190000
    gdpr_violation_average: 4800000
    sox_violation_average: 1500000

# Report Customization
reports:
  msp_branding:
    default_msp_name: "Technology Solutions Provider"
    
    # MSP name expansions for better QBR presentation
    name_expansions:
      CED: "CED Technology Solutions"
      MSP: "Managed Service Provider"
      IT: "IT Solutions Provider"
  
  contact_info:
    support_email: "<EMAIL>"
    support_phone: "(*************"
    website: "https://www.yourmsp.com"
  
  health_scoring:
    # Health score thresholds
    excellent_threshold: 90
    good_threshold: 75
    needs_attention_threshold: 60
    
    # Risk score thresholds for predictive analytics
    high_risk_threshold: 70
    medium_risk_threshold: 40

# SaaS License Optimization
saas:
  pricing:
    # Approximate monthly costs per seat type (USD)
    office365:
      user: 4.00
      shared_mailbox: 2.00
      site: 1.00
      team_site: 1.00
      team: 1.00
    google_workspace:
      user: 3.00
      shared_drive: 1.00
      team: 1.00
    default_seat_cost: 2.00
  
  optimization:
    # Days of inactivity before flagging for review
    inactive_threshold_days: 90
    
    # Days of inactivity before recommending removal
    removal_threshold_days: 180
    
    # Minimum monthly savings to trigger auto-action recommendations
    auto_action_savings_threshold: 25.00
    
    # Minimum inactive seats to generate optimization recommendations
    optimization_threshold_seats: 10

# Alert Correlation
alerts:
  correlation:
    # Time window for grouping related alerts (hours)
    correlation_window_hours: 4
    
    # Minimum alerts required to form a correlation
    min_correlation_size: 2
    
    # Critical severity threshold (devices affected)
    critical_threshold_devices: 3

# Infrastructure Analysis
infrastructure:
  capacity:
    # Storage usage threshold for capacity warnings
    storage_warning_threshold: 80
    
    # Server age threshold for replacement recommendations (years)
    server_age_threshold: 5
    
    # Network redundancy recommendations
    single_link_warning: true

# Artifact Management
artifacts:
  output_directory: "artifacts"
  filename_format: "{client_name}_QBR_{quarter}_{date}"
  keep_previous_versions: 5
  
  # File size limits (MB)
  max_report_size: 50
  max_screenshot_size: 5

# Logging
logging:
  level: "INFO"
  file: "logs/dpp-mcp.log"
  max_size_mb: 100
  backup_count: 5