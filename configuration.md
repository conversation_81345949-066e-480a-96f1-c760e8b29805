# Configuration Guide

This guide covers all configuration options available in the Datto Partner Portal MCP Server.

## Configuration File Structure

The main configuration file is `config/dpp-config.yaml`. All settings are organized into logical sections:

```yaml
api:           # Datto API connection settings
performance:   # Performance and rate limiting
qbr:          # QBR calculation parameters
reports:      # Report customization
saas:         # SaaS license optimization
alerts:       # Alert correlation settings
infrastructure: # Infrastructure analysis
artifacts:    # Output file management
logging:      # Logging configuration
```

## API Configuration

### Required Settings

```yaml
api:
  public_key: "YOUR_DATTO_PUBLIC_KEY"     # Required
  secret_key: "YOUR_DATTO_SECRET_KEY"     # Required
  base_url: "https://api.datto.com"       # Default
  timeout_seconds: 60                     # Default
  rate_limit_budget: 10000               # Default
```

**Notes:**
- Public and secret keys are obtained from your Datto Partner Portal
- Rate limit budget is per hour; adjust based on your API plan
- Timeout applies to individual API requests

## Performance Configuration

### API Call Limits

```yaml
performance:
  max_devices_per_query: 50      # BCDR devices per API call
  max_assets_per_device: 10      # Assets analyzed per device
  max_dtc_assets: 50            # DTC assets per query
  max_saas_domains: 20          # SaaS domains per query
  screenshot_limit: 8           # Screenshots in dashboards
  backup_history_days: 30       # Days of backup history
  backup_history_limit: 100    # Max backup records per asset
```

**Tuning Guidelines:**
- **Large environments (100+ devices)**: Reduce limits by 50% to prevent timeouts
- **Small environments (<25 devices)**: Can increase limits for faster data collection
- **Slow networks**: Reduce all limits and increase timeout_seconds

## QBR Business Impact Calculations

### Business Impact Settings

```yaml
qbr:
  business_impact:
    employee_hourly_cost: 75.00              # Average employee hourly cost
    productivity_loss_multiplier: 0.85       # Productivity during outages (0.0-1.0)
    it_staff_hourly_cost: 125.00            # IT staff hourly rate
    average_incident_response_hours: 4.0     # Time spent on incidents
    ransomware_cost_multiplier: 4.62        # Total cost vs ransom (IBM Security)
    default_ransom_demand: 220000.00        # Default ransom amount (FBI average)
```

**Customization by Market:**
- **High-cost markets** (SF, NYC): Increase employee_hourly_cost to $100-150
- **Lower-cost markets**: Decrease to $50-75
- **Enterprise clients**: Increase it_staff_hourly_cost to $150-200
- **SMB clients**: May use $75-100

### Recovery Time Estimates

```yaml
qbr:
  recovery_times:
    hardware_failure: 240      # 4 hours
    ransomware: 720           # 12 hours
    human_error: 60           # 1 hour
    software_corruption: 180   # 3 hours
    natural_disaster: 1440     # 24 hours
    cyber_attack: 480         # 8 hours
```

**Adjustment Guidelines:**
- Base times on your actual recovery experience
- Conservative estimates build credibility
- Include time for verification and testing

### Storage Cost Calculations

```yaml
qbr:
  storage_costs:
    cost_per_gb_month: 0.12                      # Storage cost per GB/month
    deduplication_ratio: 3.5                     # Datto deduplication efficiency
    alternative_storage_cost_multiplier: 1.8     # Cost without Datto
```

### Compliance Violation Costs

```yaml
qbr:
  compliance:
    hipaa_violation_average: 2300000      # Healthcare
    pci_violation_average: 190000         # Payment processing
    gdpr_violation_average: 4800000       # European operations
    sox_violation_average: 1500000        # Public companies
```

## Report Customization

### MSP Branding

```yaml
reports:
  msp_branding:
    default_msp_name: "Your MSP Name Here"
    
    # Expand abbreviations for professional presentation
    name_expansions:
      CED: "CED Technology Solutions"
      ABC: "ABC Managed Services"
      # Add your abbreviations here
```

### Contact Information

```yaml
reports:
  contact_info:
    support_email: "<EMAIL>"
    support_phone: "(*************"
    website: "https://www.yourmsp.com"
```

### Health Scoring Thresholds

```yaml
reports:
  health_scoring:
    excellent_threshold: 90        # 90%+ = Excellent
    good_threshold: 75            # 75-89% = Good
    needs_attention_threshold: 60  # 60-74% = Needs Attention
    high_risk_threshold: 70       # 70%+ = High Risk
    medium_risk_threshold: 40     # 40-69% = Medium Risk
```

## SaaS License Optimization

### Pricing Configuration

```yaml
saas:
  pricing:
    office365:
      user: 4.00
      shared_mailbox: 2.00
      site: 1.00
      team_site: 1.00
      team: 1.00
    google_workspace:
      user: 3.00
      shared_drive: 1.00
      team: 1.00
    default_seat_cost: 2.00
```

**Customization:**
- Update prices to match your actual SaaS Protection rates
- Add new platforms as needed
- Consider volume discounts for large deployments

### Optimization Thresholds

```yaml
saas:
  optimization:
    inactive_threshold_days: 90           # Flag seats inactive this long
    removal_threshold_days: 180          # Recommend removal after this
    auto_action_savings_threshold: 25.00 # Min $ to suggest auto-actions
    optimization_threshold_seats: 10     # Min seats to show optimization
```

## Alert Correlation

### Correlation Settings

```yaml
alerts:
  correlation:
    correlation_window_hours: 4          # Group alerts within this timeframe
    min_correlation_size: 2             # Minimum alerts to form correlation
    critical_threshold_devices: 3       # Devices affected = critical severity
```

## Infrastructure Analysis

### Capacity Planning

```yaml
infrastructure:
  capacity:
    storage_warning_threshold: 80       # Warn at 80% storage usage
    server_age_threshold: 5            # Flag servers over 5 years old
    single_link_warning: true          # Warn about single points of failure
```

## Artifact Management

### File Output Settings

```yaml
artifacts:
  output_directory: "artifacts"                           # Output folder
  filename_format: "{client_name}_QBR_{quarter}_{date}"  # File naming
  keep_previous_versions: 5                               # Versions to retain
  max_report_size: 50                                     # Max size in MB
  max_screenshot_size: 5                                  # Max screenshot size
```

**Filename Variables:**
- `{client_name}`: Sanitized client name
- `{quarter}`: Quarter (e.g., Q2_2025)
- `{date}`: Current date (YYYYMMDD)
- `{timestamp}`: Full timestamp

## Logging Configuration

### Log Settings

```yaml
logging:
  level: "INFO"                    # DEBUG, INFO, WARNING, ERROR
  file: "logs/dpp-mcp.log"        # Log file location
  max_size_mb: 100                # Max log file size
  backup_count: 5                 # Number of backup files
```

**Log Levels:**
- **DEBUG**: Detailed information for troubleshooting
- **INFO**: General operational information
- **WARNING**: Important events that don't stop operation
- **ERROR**: Error events that may affect functionality

## Environment-Specific Configurations

### Development Environment

```yaml
api:
  timeout_seconds: 30
performance:
  max_devices_per_query: 10
  screenshot_limit: 4
logging:
  level: "DEBUG"
```

### Production Environment

```yaml
api:
  timeout_seconds: 60
  rate_limit_budget: 10000
performance:
  max_devices_per_query: 50
  max_assets_per_device: 10
logging:
  level: "INFO"
  max_size_mb: 500
  backup_count: 10
```

### High-Volume Environment

```yaml
performance:
  max_devices_per_query: 25
  max_assets_per_device: 5
  backup_history_limit: 50
api:
  timeout_seconds: 90
```

## Configuration Validation

### Syntax Validation

```bash
# Validate YAML syntax
python -c "import yaml; yaml.safe_load(open('config/dpp-config.yaml'))"
```

### Testing Configuration

```bash
# Test with new configuration
python src/dpp_mcp_server.py --config-test

# Dry run QBR generation
python src/dpp_mcp_server.py --dry-run
```

## Configuration Security

### Best Practices

1. **Never commit API credentials** to version control
2. **Use environment variables** for sensitive data
3. **Set appropriate file permissions** (600) on configuration files
4. **Regularly rotate API keys** according to your security policy
5. **Audit configuration changes** in production environments

### Environment Variable Override

Any configuration value can be overridden with environment variables:

```bash
# Override API credentials
export DATTO_PUBLIC_KEY="new_key"
export DATTO_SECRET_KEY="new_secret"

# Override other settings
export DPP_EMPLOYEE_HOURLY_COST="100.00"
export DPP_MAX_DEVICES="25"
```

Variable naming convention: `DPP_` + uppercase setting path with underscores.

## Common Configuration Scenarios

### Scenario 1: Small MSP (< 50 clients)

```yaml
performance:
  max_devices_per_query: 50
  max_saas_domains: 20
qbr:
  business_impact:
    employee_hourly_cost: 65.00
    it_staff_hourly_cost: 100.00
```

### Scenario 2: Large MSP (100+ clients)

```yaml
performance:
  max_devices_per_query: 25
  max_assets_per_device: 5
  backup_history_limit: 50
api:
  timeout_seconds: 90
  rate_limit_budget: 20000
```

### Scenario 3: Enterprise Focus

```yaml
qbr:
  business_impact:
    employee_hourly_cost: 125.00
    it_staff_hourly_cost: 175.00
    default_ransom_demand: 500000.00
reports:
  health_scoring:
    excellent_threshold: 95
    good_threshold: 85
```

## Configuration Troubleshooting

### Common Issues

1. **YAML syntax errors**: Use online YAML validators
2. **Invalid API credentials**: Test with Datto Portal
3. **Performance timeouts**: Reduce query limits
4. **Missing sections**: Copy from config-example.yaml
5. **Permission denied**: Check file permissions

### Diagnostic Commands

```bash
# Check configuration loading
python -c "from src.dpp_mcp_server import load_config; print(load_config())"

# Verify API connection
python -c "from src.utils.api_client import DattoClient; import asyncio; print('API test')"

# Test business calculations
python -c "from src.qbr.metrics import BusinessImpactCalculator; print('Calculator ready')"
```