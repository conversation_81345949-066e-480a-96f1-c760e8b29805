# Deployment Guide

This guide covers deploying the Datto Partner MCP Server in various environments for production use.

## 🚀 Deployment Options

### 1. Local Development
Perfect for testing and development.

```bash
# Clone repository
git clone <repository-url>
cd datto-partner-mcp

# Setup environment
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Configure
cp config/partner_config.yaml.example config/partner_config.yaml
# Edit configuration file

# Run
python src/datto_mcp.py
```

### 2. Docker Deployment
Containerized deployment for consistent environments.

**Dockerfile**
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY src/ ./src/
COPY config/ ./config/
COPY templates/ ./templates/

# Create logs directory
RUN mkdir -p logs

# Non-root user
RUN useradd -m -u 1000 datto && chown -R datto:datto /app
USER datto

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python src/health_check.py || exit 1

EXPOSE 8000
CMD ["python", "src/datto_mcp.py"]
```

**docker-compose.yml**
```yaml
version: '3.8'

services:
  datto-mcp:
    build: .
    container_name: datto-partner-mcp
    restart: unless-stopped
    environment:
      - DATTO_PUBLIC_KEY=${DATTO_PUBLIC_KEY}
      - DATTO_SECRET_KEY=${DATTO_SECRET_KEY}
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
      - ./data:/app/data
    ports:
      - "8000:8000"
    healthcheck:
      test: ["CMD", "python", "src/health_check.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    container_name: datto-mcp-redis
    restart: unless-stopped
    volumes:
      - redis-data:/data
    command: redis-server --maxmemory 256mb --maxmemory-policy allkeys-lru

volumes:
  redis-data:
```

### 3. Cloud Deployment (AWS ECS)
Scalable cloud deployment.

**task-definition.json**
```json
{
  "family": "datto-partner-mcp",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/dattoMcpTaskRole",
  "containerDefinitions": [
    {
      "name": "datto-mcp",
      "image": "your-registry/datto-partner-mcp:latest",
      "essential": true,
      "portMappings": [
        {
          "containerPort": 8000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "ENVIRONMENT",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATTO_PUBLIC_KEY",
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:datto-api-keys:public_key"
        },
        {
          "name": "DATTO_SECRET_KEY", 
          "valueFrom": "arn:aws:secretsmanager:region:account:secret:datto-api-keys:secret_key"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/datto-partner-mcp",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "python src/health_check.py"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

### 4. Kubernetes Deployment
Enterprise-grade orchestration.

**deployment.yaml**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: datto-partner-mcp
  labels:
    app: datto-partner-mcp
spec:
  replicas: 2
  selector:
    matchLabels:
      app: datto-partner-mcp
  template:
    metadata:
      labels:
        app: datto-partner-mcp
    spec:
      containers:
      - name: datto-mcp
        image: your-registry/datto-partner-mcp:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATTO_PUBLIC_KEY
          valueFrom:
            secretKeyRef:
              name: datto-api-credentials
              key: public-key
        - name: DATTO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: datto-api-credentials
              key: secret-key
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /app/logs
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
          requests:
            cpu: 250m
            memory: 256Mi
        livenessProbe:
          exec:
            command:
            - python
            - src/health_check.py
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - python
            - src/health_check.py
          initialDelaySeconds: 10
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: datto-mcp-config
      - name: logs
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: datto-partner-mcp-service
spec:
  selector:
    app: datto-partner-mcp
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP
```

## 🔐 Security Configuration

### Environment Variables
Never hardcode sensitive data. Use environment variables or secrets management.

```bash
# Required
export DATTO_PUBLIC_KEY="your-api-public-key"
export DATTO_SECRET_KEY="your-api-secret-key"

# Optional
export MCP_LOG_LEVEL="INFO"
export MCP_CONFIG_PATH="/app/config/partner_config.yaml"
```

### Secrets Management

**AWS Secrets Manager**
```python
import boto3

def get_datto_credentials():
    client = boto3.client('secretsmanager')
    response = client.get_secret_value(SecretId='datto-api-credentials')
    credentials = json.loads(response['SecretString'])
    return credentials['public_key'], credentials['secret_key']
```

**Kubernetes Secrets**
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: datto-api-credentials
type: Opaque
data:
  public-key: <base64-encoded-public-key>
  secret-key: <base64-encoded-secret-key>
```

### Network Security
- Use HTTPS/TLS for all communications
- Implement IP whitelisting if possible
- Use VPC/network segmentation in cloud deployments
- Regular security updates and vulnerability scanning

## 📊 Monitoring & Logging

### Application Metrics
```python
# Health check endpoint
@app.route('/health')
def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "2.0.0",
        "api_status": check_datto_api(),
        "features_enabled": get_enabled_features()
    }
```

### Structured Logging
```yaml
logging:
  level: "INFO"
  format: "json"
  handlers:
    - type: "file"
      filename: "/app/logs/datto_mcp.log"
      max_size: "10MB"
      backup_count: 5
    - type: "console"
      stream: "stdout"
```

### Monitoring Stack
- **Metrics**: Prometheus + Grafana
- **Logs**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **APM**: New Relic or DataDog
- **Alerting**: PagerDuty or Slack integration

## 🔄 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy Datto Partner MCP

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-cov
    - name: Run tests
      run: pytest tests/ --cov=src/
    - name: Lint code
      run: |
        pip install black flake8
        black --check src/
        flake8 src/

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v4
    - name: Build Docker image
      run: docker build -t datto-partner-mcp:${{ github.sha }} .
    - name: Push to registry
      run: |
        echo ${{ secrets.REGISTRY_PASSWORD }} | docker login -u ${{ secrets.REGISTRY_USERNAME }} --password-stdin
        docker push datto-partner-mcp:${{ github.sha }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Deploy to production
      run: |
        # Update ECS service, Kubernetes deployment, etc.
        aws ecs update-service --cluster prod --service datto-mcp --force-new-deployment
```

## 🔧 Performance Optimization

### Caching Strategy
```python
import redis
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_result(expiry=300):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cached = redis_client.get(cache_key)
            if cached:
                return json.loads(cached)
            
            result = await func(*args, **kwargs)
            redis_client.setex(cache_key, expiry, json.dumps(result))
            return result
        return wrapper
    return decorator
```

### Rate Limiting
```python
from collections import defaultdict
import time

class RateLimiter:
    def __init__(self, max_requests=100, window=3600):
        self.max_requests = max_requests
        self.window = window
        self.requests = defaultdict(list)
    
    def allow_request(self, client_id):
        now = time.time()
        client_requests = self.requests[client_id]
        
        # Remove old requests
        self.requests[client_id] = [
            req_time for req_time in client_requests 
            if now - req_time < self.window
        ]
        
        if len(self.requests[client_id]) >= self.max_requests:
            return False
        
        self.requests[client_id].append(now)
        return True
```

### Database Optimization
```python
# Connection pooling
import asyncpg

async def create_db_pool():
    return await asyncpg.create_pool(
        database="datto_mcp",
        user="app_user",
        password="secure_password",
        host="db_host",
        min_size=5,
        max_size=20,
        command_timeout=60
    )

# Prepared statements
async def get_device_health(pool, device_id):
    async with pool.acquire() as conn:
        stmt = await conn.prepare(
            "SELECT health_score, last_update FROM device_health WHERE device_id = $1"
        )
        return await stmt.fetchrow(device_id)
```

## 🚨 Disaster Recovery

### Backup Strategy
- **Configuration**: Store in version control
- **Logs**: Archive to S3/cloud storage
- **State**: Use stateless design where possible
- **Database**: Regular backups with point-in-time recovery

### Recovery Procedures
1. **Service Outage**: Auto-scaling and health checks
2. **Data Corruption**: Restore from backups
3. **Security Incident**: Rotate credentials, audit logs
4. **Regional Failure**: Multi-region deployment

### Runbooks
Create detailed runbooks for common scenarios:
- API credential rotation
- Service scaling during high load
- Log analysis for troubleshooting
- Partner onboarding procedures

## 📋 Maintenance

### Regular Tasks
- **Daily**: Monitor health checks and logs
- **Weekly**: Review performance metrics and alerts
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Capacity planning and cost optimization

### Update Procedures
1. Test in development environment
2. Deploy to staging for validation
3. Blue-green deployment to production
4. Monitor for issues and rollback if needed

---

**Need help with deployment? Contact the Datto Partner Success Team.**