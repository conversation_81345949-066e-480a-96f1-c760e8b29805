#!/usr/bin/env python3
"""
Usage Examples for Datto Partner MCP Server
Demonstrates how to interact with the MCP server through various clients.
"""

import asyncio
import json
from datetime import datetime

# Example MCP client interactions
# These would typically be called through <PERSON> Desktop or other MCP clients

async def basic_monitoring_examples():
    """Basic monitoring and health check examples."""
    
    print("=== Basic Monitoring Examples ===")
    
    # Get overall backup health
    print("\n1. Check Overall Backup Health:")
    print("Tool: analyze_backup_health()")
    print("Returns: Health score, status, device counts, recommendations")
    
    # List protected devices
    print("\n2. List All BCDR Devices:")
    print("Tool: list_bcdr_devices(page=1, per_page=50)")
    print("Returns: Device inventory with alerting status")
    
    # System health check
    print("\n3. Comprehensive System Health:")
    print("Tool: system_health_check()")
    print("Returns: API status, feature status, rate limits, configuration validation")

async def intelligent_analytics_examples():
    """AI-powered analytics examples."""
    
    print("\n=== Intelligent Analytics Examples ===")
    
    # Alert correlation
    print("\n1. AI-Powered Alert Correlation:")
    print("Tool: correlate_alerts()")
    print("Returns: Grouped alerts, root cause analysis, noise reduction metrics")
    
    # Predictive risk assessment
    print("\n2. Backup Failure Prediction:")
    print("Tool: predict_backup_risks()")
    print("Returns: High/medium/low risk assets with time windows and recommendations")
    
    # SaaS license optimization
    print("\n3. SaaS License Waste Identification:")
    print("Tool: optimize_saas_licensing()")
    print("Returns: Unused seats, potential savings, auto-actions")

async def executive_reporting_examples():
    """Executive dashboard and QBR examples."""
    
    print("\n=== Executive Reporting Examples ===")
    
    # Token-efficient dashboard
    print("\n1. Executive Dashboard:")
    print("Resource: datto://executive-dashboard")
    print("Returns: Token-efficient HTML dashboard with partner branding")
    
    # QBR generation
    print("\n2. Quarterly Business Review:")
    print("Tool: create_qbr(client_name='Acme Corp', quarter='Q1 2025')")
    print("Returns: Comprehensive QBR with ROI analysis and MSP recommendations")
    
    # Business impact calculation
    print("\n3. Business Impact Calculator:")
    print("""Tool: calculate_business_impact(
    recovery_type='ransomware',
    downtime_hours=4.0,
    employees_affected=50,
    ransom_demand=250000
)""")
    print("Returns: Detailed cost analysis with industry benchmarks")

def partner_configuration_example():
    """Example partner configuration."""
    
    config_example = {
        "partner": {
            "name": "TechGuard MSP",
            "initials": "TGM",
            "support_email": "<EMAIL>",
            "emergency_phone": "1-800-TECHGUARD",
            "website": "https://techguardmsp.com",
            "logo_url": "https://techguardmsp.com/logo.png"
        },
        "datto": {
            "public_key": "${DATTO_PUBLIC_KEY}",
            "secret_key": "${DATTO_SECRET_KEY}",
            "base_url": "https://api.datto.com",
            "rate_limit_budget": 10000
        },
        "qbr": {
            "default_quarter": "auto",
            "include_screenshots": True,
            "cost_calculations": {
                "small_business_downtime_cost": 8600,
                "mid_market_downtime_cost": 35000,
                "enterprise_downtime_cost": 100000,
                "compliance_value_per_user": 150
            }
        },
        "features": {
            "intelligent_alert_correlation": True,
            "predictive_risk_assessment": True,
            "saas_license_optimization": True,
            "screenshot_verification": True,
            "interactive_qbr": True,
            "business_impact_calculations": True
        },
        "dashboard": {
            "theme": "dark",
            "company_colors": {
                "primary": "#2563eb",
                "secondary": "#7c3aed", 
                "accent": "#06b6d4"
            },
            "refresh_interval": 300
        }
    }
    
    print("\n=== Partner Configuration Example ===")
    print(json.dumps(config_example, indent=2))

def claude_desktop_setup_example():
    """Example Claude Desktop configuration."""
    
    claude_config = {
        "mcpServers": {
            "datto-partner": {
                "command": "python",
                "args": ["/Users/<USER>/datto-partner-mcp/src/datto_mcp.py"],
                "env": {
                    "DATTO_PUBLIC_KEY": "your-api-public-key",
                    "DATTO_SECRET_KEY": "your-api-secret-key"
                }
            }
        }
    }
    
    print("\n=== Claude Desktop Configuration ===")
    print("File: ~/.config/claude-desktop/config.json")
    print(json.dumps(claude_config, indent=2))

def sample_interactions():
    """Sample conversation flows with Claude."""
    
    print("\n=== Sample Claude Interactions ===")
    
    interactions = [
        {
            "user": "Show me the health status of all my backup systems",
            "assistant_action": "analyze_backup_health()",
            "result": "Health analysis with device counts, alert summary, and recommendations"
        },
        {
            "user": "Create a Q1 2025 QBR for Acme Corporation",
            "assistant_action": "create_qbr(client_name='Acme Corporation', quarter='Q1 2025')",
            "result": "Comprehensive QBR with ROI analysis, recovery events, and MSP recommendations"
        },
        {
            "user": "What alerts are related and what should I prioritize?",
            "assistant_action": "correlate_alerts()",
            "result": "Grouped alerts with root cause analysis and prioritized action items"
        },
        {
            "user": "Which backup systems are likely to fail soon?",
            "assistant_action": "predict_backup_risks()",
            "result": "Risk assessment with time windows and preventive actions"
        },
        {
            "user": "How much money am I wasting on unused SaaS licenses?",
            "assistant_action": "optimize_saas_licensing()",
            "result": "License waste analysis with specific savings opportunities"
        },
        {
            "user": "Show me an executive dashboard for the board meeting",
            "assistant_action": "Access resource: datto://executive-dashboard",
            "result": "Token-efficient dashboard with partner branding and key metrics"
        }
    ]
    
    for i, interaction in enumerate(interactions, 1):
        print(f"\n{i}. User Request: {interaction['user']}")
        print(f"   Assistant Action: {interaction['assistant_action']}")
        print(f"   Result: {interaction['result']}")

def roi_calculation_examples():
    """ROI calculation examples for different scenarios."""
    
    print("\n=== ROI Calculation Examples ===")
    
    scenarios = [
        {
            "name": "Ransomware Attack Prevention",
            "parameters": {
                "recovery_type": "ransomware",
                "downtime_hours": 6.0,
                "employees_affected": 75,
                "ransom_demand": 280000
            },
            "expected_savings": "$1,200,000+ (4.62x multiplier + downtime costs)"
        },
        {
            "name": "Hardware Failure Recovery",
            "parameters": {
                "recovery_type": "hardware_failure", 
                "downtime_hours": 2.0,
                "employees_affected": 50
            },
            "expected_savings": "$70,000 (2 hours × $35K/hour mid-market rate)"
        },
        {
            "name": "Human Error Data Loss",
            "parameters": {
                "recovery_type": "human_error",
                "downtime_hours": 0.5,
                "employees_affected": 25
            },
            "expected_savings": "$8,600 (0.5 hours × $17.2K scaled rate)"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        print(f"  Parameters: {scenario['parameters']}")
        print(f"  Expected Value: {scenario['expected_savings']}")

def main():
    """Run all examples."""
    print("Datto Partner MCP Server - Usage Examples")
    print("=" * 50)
    
    asyncio.run(basic_monitoring_examples())
    asyncio.run(intelligent_analytics_examples()) 
    asyncio.run(executive_reporting_examples())
    
    partner_configuration_example()
    claude_desktop_setup_example()
    sample_interactions()
    roi_calculation_examples()
    
    print("\n" + "=" * 50)
    print("For more information, see README.md and configuration documentation.")

if __name__ == "__main__":
    main()