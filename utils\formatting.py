"""
Formatting utilities for data display and report generation.
"""

from typing import Union
from datetime import datetime


def format_storage_size(gb_value: Union[int, float]) -> str:
    """Format storage size in appropriate units (GB/TB)."""
    if gb_value >= 1024:
        return f"{gb_value / 1024:.1f}TB"
    else:
        return f"{gb_value:.0f}GB"


def format_currency(amount: Union[int, float], currency: str = "USD") -> str:
    """Format currency amounts with proper symbols."""
    if currency.upper() == "USD":
        return f"${amount:,.2f}"
    else:
        return f"{amount:,.2f} {currency}"


def format_percentage(value: Union[int, float], decimal_places: int = 1) -> str:
    """Format percentage values."""
    return f"{value:.{decimal_places}f}%"


def format_timestamp(timestamp: Union[int, float, str], format_str: str = "%m/%d %H:%M") -> str:
    """Format Unix timestamps or ISO strings for display."""
    if isinstance(timestamp, str):
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime(format_str)
        except (ValueError, AttributeError):
            return "Invalid date"
    
    try:
        if timestamp > 0:
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime(format_str)
        else:
            return "No recent backup"
    except (ValueError, OSError):
        return "Invalid date"


def format_duration_hours(hours: Union[int, float]) -> str:
    """Format duration in hours to human-readable format."""
    if hours < 1:
        minutes = int(hours * 60)
        return f"{minutes} minutes"
    elif hours < 24:
        return f"{hours:.1f} hours"
    else:
        days = int(hours // 24)
        remaining_hours = int(hours % 24)
        if remaining_hours > 0:
            return f"{days} days, {remaining_hours} hours"
        else:
            return f"{days} days"


def sanitize_filename(filename: str) -> str:
    """Sanitize filename by removing invalid characters."""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename.strip()


def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length with suffix."""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix


def format_device_status(alert_count: int) -> str:
    """Format device status based on alert count."""
    if alert_count == 0:
        return "Healthy"
    elif alert_count <= 2:
        return "Needs Attention"
    else:
        return "Critical"


def format_health_score(score: Union[int, float]) -> str:
    """Format health score with status description."""
    if score >= 90:
        return f"{score:.1f}% (Excellent)"
    elif score >= 75:
        return f"{score:.1f}% (Good)"
    elif score >= 60:
        return f"{score:.1f}% (Fair)"
    else:
        return f"{score:.1f}% (Needs Attention)"


def format_list_summary(items: list, max_items: int = 3) -> str:
    """Format a list as a summary string."""
    if not items:
        return "None"
    
    if len(items) <= max_items:
        return ", ".join(str(item) for item in items)
    else:
        shown = ", ".join(str(item) for item in items[:max_items])
        remaining = len(items) - max_items
        return f"{shown} and {remaining} more"